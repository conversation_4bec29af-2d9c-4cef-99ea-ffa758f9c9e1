# WOSS Seismic Analysis Tool: GPU-Prioritized Refactoring Guide

## Executive Summary

This document provides a comprehensive refactoring implementation guide for transforming the monolithic WOSS Seismic Analysis Tool into a modular, GPU-optimized Streamlit application. The refactoring **prioritizes GPU processing** for all analysis modes: inline, crossline, AOI, and polyline.

## Project Overview

**Current State:** Single `app_ref.py` file (~4600+ lines) with mixed UI, business logic, and processing code
**Target State:** Modular GPU-first architecture with clear separation of concerns

### Key Requirements
- **GPU-First Processing:** All spectral analysis prioritizes GPU via CuPy with CPU fallback
- **All Analysis Modes Supported:** Inline, crossline, AOI, and polyline with GPU optimization
- **Modular Architecture:** Clean separation of UI, business logic, and utilities
- **High Performance:** Leverage GPU acceleration for computational tasks
- **Maintainable:** Easy to modify, test, and extend

---

## Phase 0: GPU-First Foundation

**⚠️ PRIORITY: Establish GPU-First Architecture ⚠️**

The refactoring prioritizes GPU processing while maintaining CPU fallback capability.

### Step 0.1: GPU Infrastructure Setup

**Objective:** Establish GPU-first processing infrastructure

**Action:** Create GPU utilities module `utils/gpu_utils.py`:

```python
# utils/gpu_utils.py
import logging
import streamlit as st

# Global GPU state
GPU_AVAILABLE = False
CPU_FALLBACK_ENABLED = True

@st.cache_resource
def initialize_gpu_system():
    """Initialize GPU processing with fallback detection."""
    global GPU_AVAILABLE
    
    try:
        import cupy as cp
        # Test GPU functionality
        test_array = cp.array([1, 2, 3])
        test_result = cp.sum(test_array)
        
        GPU_AVAILABLE = True
        logging.info("GPU processing enabled - CuPy available")
        return True, "CuPy", cp.cuda.Device().compute_capability
        
    except ImportError:
        logging.warning("CuPy not available - using CPU fallback")
        GPU_AVAILABLE = False
        return False, "CPU Fallback", None
    except Exception as e:
        logging.error(f"GPU initialization failed: {e}")
        GPU_AVAILABLE = False
        return False, "CPU Fallback", str(e)

def get_processing_backend():
    """Get the active processing backend (GPU or CPU)."""
    if GPU_AVAILABLE:
        try:
            import cupy as cp
            from utils.dlogst_spec_descriptor_gpu import dlogst_spec_descriptor_gpu
            return "GPU", dlogst_spec_descriptor_gpu
        except ImportError:
            pass
    
    # Fallback to CPU
    from utils.dlogst_spec_descriptor_cpu import dlogst_spec_descriptor_cpu
    return "CPU", dlogst_spec_descriptor_cpu

def optimize_batch_size_for_mode(mode, base_size=512):
    """Optimize batch size based on analysis mode and GPU memory."""
    if not GPU_AVAILABLE:
        return min(base_size // 4, 128)  # Conservative CPU batch size
    
    try:
        import cupy as cp
        mempool = cp.get_default_memory_pool()
        free_bytes = mempool.free_bytes()
        total_bytes = mempool.total_bytes()
        
        # Mode-specific optimization
        if mode in ["Single inline (all crosslines)", "Single crossline (all inlines)"]:
            return min(base_size * 2, 1024)  # Larger batches for line processing
        elif mode == "By inline/crossline section (AOI)":
            return base_size  # Standard batch size for AOI
        elif mode == "By Polyline File Import":
            return min(base_size // 2, 256)  # Smaller batches for complex polyline processing
        else:
            return base_size
            
    except Exception:
        return base_size
```

### Step 0.2: Enhanced Processing Module

**Objective:** Create GPU-prioritized processing functions for all modes

**Action:** Create `utils/processing_gpu.py`:

```python
# utils/processing_gpu.py
import numpy as np
import logging
from .gpu_utils import get_processing_backend, optimize_batch_size_for_mode

def process_inline_mode_gpu(segy_path, inline_number, header_loader, dt, plot_settings):
    """GPU-optimized processing for single inline mode."""
    backend_type, descriptor_func = get_processing_backend()
    batch_size = optimize_batch_size_for_mode("Single inline (all crosslines)")
    
    logging.info(f"Processing inline {inline_number} using {backend_type} backend")
    
    # Get all traces for this inline
    inline_mask = header_loader.inlines == inline_number
    trace_indices = header_loader.unique_indices[inline_mask]
    
    return process_traces_gpu_batch(segy_path, trace_indices, dt, plot_settings, 
                                   descriptor_func, batch_size)

def process_crossline_mode_gpu(segy_path, crossline_number, header_loader, dt, plot_settings):
    """GPU-optimized processing for single crossline mode."""
    backend_type, descriptor_func = get_processing_backend()
    batch_size = optimize_batch_size_for_mode("Single crossline (all inlines)")
    
    logging.info(f"Processing crossline {crossline_number} using {backend_type} backend")
    
    # Get all traces for this crossline
    crossline_mask = header_loader.crosslines == crossline_number
    trace_indices = header_loader.unique_indices[crossline_mask]
    
    return process_traces_gpu_batch(segy_path, trace_indices, dt, plot_settings, 
                                   descriptor_func, batch_size)

def process_aoi_mode_gpu(segy_path, inline_range, crossline_range, header_loader, dt, plot_settings):
    """GPU-optimized processing for AOI (Area of Interest) mode."""
    backend_type, descriptor_func = get_processing_backend()
    batch_size = optimize_batch_size_for_mode("By inline/crossline section (AOI)")
    
    logging.info(f"Processing AOI using {backend_type} backend")
    
    # Get traces within AOI bounds
    inline_mask = (header_loader.inlines >= inline_range[0]) & (header_loader.inlines <= inline_range[1])
    crossline_mask = (header_loader.crosslines >= crossline_range[0]) & (header_loader.crosslines <= crossline_range[1])
    aoi_mask = inline_mask & crossline_mask
    trace_indices = header_loader.unique_indices[aoi_mask]
    
    return process_traces_gpu_batch(segy_path, trace_indices, dt, plot_settings, 
                                   descriptor_func, batch_size)

def process_polyline_mode_gpu(segy_path, polyline_indices, header_loader, dt, plot_settings):
    """GPU-optimized processing for polyline mode."""
    backend_type, descriptor_func = get_processing_backend()
    batch_size = optimize_batch_size_for_mode("By Polyline File Import")
    
    logging.info(f"Processing polyline with {len(polyline_indices)} traces using {backend_type} backend")
    
    return process_traces_gpu_batch(segy_path, polyline_indices, dt, plot_settings, 
                                   descriptor_func, batch_size)

def process_traces_gpu_batch(segy_path, trace_indices, dt, plot_settings, descriptor_func, batch_size):
    """Core GPU batch processing function for all modes."""
    backend_type, _ = get_processing_backend()
    
    if backend_type == "GPU":
        return _process_gpu_optimized(segy_path, trace_indices, dt, plot_settings, descriptor_func, batch_size)
    else:
        return _process_cpu_fallback(segy_path, trace_indices, dt, plot_settings, descriptor_func, batch_size)

def _process_gpu_optimized(segy_path, trace_indices, dt, plot_settings, descriptor_func, batch_size):
    """GPU-optimized batch processing implementation."""
    import cupy as cp
    from tqdm import tqdm
    
    all_trace_data = []
    all_descriptors = []
    
    # Process in optimized GPU batches
    for i in tqdm(range(0, len(trace_indices), batch_size), desc="GPU Processing"):
        batch_indices = trace_indices[i:i + batch_size]
        
        # Load batch to GPU memory
        batch_traces = []
        for idx in batch_indices:
            trace = load_trace_sample(segy_path, idx)
            batch_traces.append(trace)
        
        # Convert to GPU arrays
        gpu_traces = cp.array(batch_traces, dtype=cp.float32)
        
        # Process entire batch on GPU
        batch_descriptors = descriptor_func(gpu_traces, dt, **plot_settings)
        
        # Convert results back to CPU for storage
        cpu_descriptors = {}
        for key, value in batch_descriptors.items():
            if hasattr(value, 'get'):  # CuPy array
                cpu_descriptors[key] = cp.asnumpy(value)
            else:
                cpu_descriptors[key] = value
        
        all_trace_data.extend(batch_traces)
        all_descriptors.append(cpu_descriptors)
        
        # Clear GPU memory
        del gpu_traces, batch_descriptors
        cp.get_default_memory_pool().free_all_blocks()
    
    return all_trace_data, all_descriptors

def _process_cpu_fallback(segy_path, trace_indices, dt, plot_settings, descriptor_func, batch_size):
    """CPU fallback processing implementation."""
    from tqdm import tqdm
    
    all_trace_data = []
    all_descriptors = []
    
    # Smaller batches for CPU processing
    cpu_batch_size = min(batch_size // 4, 32)
    
    for i in tqdm(range(0, len(trace_indices), cpu_batch_size), desc="CPU Processing"):
        batch_indices = trace_indices[i:i + cpu_batch_size]
        
        for idx in batch_indices:
            trace = load_trace_sample(segy_path, idx)
            descriptors = descriptor_func(trace, dt, **plot_settings)
            
            all_trace_data.append(trace)
            all_descriptors.append(descriptors)
    
    return all_trace_data, all_descriptors
```
---

## Phase 1: Foundation Setup (Duration: 1-2 days)

### Phase 1.1: Directory Structure Creation

**Objective:** Establish modular architecture optimized for GPU processing

**Action:** Create the following GPU-optimized directory structure:
```
4a_Eframework_v1/
├── app.py                          # Main GPU-aware application router
├── common/                         # Shared GPU/CPU resources
│   ├── __init__.py
│   ├── constants.py               # GPU-optimized constants
│   ├── session_state.py          # GPU state management
│   └── ui_elements.py            # GPU-aware UI components
├── pages/                         # GPU-optimized page modules
│   ├── __init__.py
│   ├── 1_load_data.py            # Data loading with GPU preparation
│   ├── 2_configure_display.py    # GPU parameter configuration
│   ├── 3_select_area.py          # Mode selection for GPU processing
│   ├── 4_analyze_data.py         # GPU-first analysis execution
│   └── 5_export_results.py       # GPU-accelerated export
├── utils/                         # GPU-prioritized backend utilities
│   ├── __init__.py
│   ├── gpu_utils.py              # GPU initialization and management
│   ├── processing_gpu.py         # GPU-first processing functions
│   ├── data_utils.py             # GPU-aware data loading
│   ├── visualization.py          # GPU-optimized plotting
│   ├── export_utils.py           # GPU-accelerated export
│   ├── dlogst_spec_descriptor_gpu.py  # GPU implementation (existing)
│   └── dlogst_spec_descriptor_cpu.py  # CPU fallback (existing)
└── requirements.txt               # GPU + CPU dependencies
```

### Phase 1.2: GPU-Optimized Constants

**File:** `common/constants.py`

```python
# common/constants.py
APP_TITLE = "WOSS Seismic Analysis Tool (GPU-Accelerated)"

# GPU Processing Configuration
GPU_PROCESSING_PREFERRED = True
GPU_BATCH_SIZES = {
    "Single inline (all crosslines)": 1024,
    "Single crossline (all inlines)": 1024, 
    "By inline/crossline section (AOI)": 512,
    "By Polyline File Import": 256,
    "By well markers": 128
}

CPU_FALLBACK_BATCH_SIZES = {
    "Single inline (all crosslines)": 64,
    "Single crossline (all inlines)": 64,
    "By inline/crossline section (AOI)": 32, 
    "By Polyline File Import": 16,
    "By well markers": 8
}

# Analysis Modes (GPU-optimized)
ANALYSIS_MODES = [
    "Single inline (all crosslines)",    # GPU-optimized for line processing
    "Single crossline (all inlines)",    # GPU-optimized for line processing  
    "By inline/crossline section (AOI)", # GPU-optimized for area processing
    "By Polyline File Import",           # GPU-optimized for polyline processing
    "By well markers"                    # GPU-optimized for point processing
]

# GPU-Optimized Output Types
AVAILABLE_OUTPUTS_ALL_MODES = [
    "Input Signal", "HFC", "Spectral Decrease", "Spectral Slope",
    "Mag*Voice Slope", "Voice Slope", "Peak Frequency", 
    "Spectral Centroid", "Dominant Frequency", "Spectral Bandwidth",
    "Spectral Rolloff", "Magnitude Spectrogram", "Magnitude * Voice",
    "WOSS", "Normalized Dominant Frequency"
]

# Export attributes optimized for GPU processing
EXPORTABLE_ATTRIBUTES = {
    "data": "Original Seismic Amplitude",
    "hfc": "High Frequency Content", 
    "spec_decrease": "Spectral Decrease",
    "spec_slope": "Spectral Slope",
    "mag_voice_slope": "Magnitude*Voice Slope",
    "spec_bandwidth": "Spectral Bandwidth", 
    "spec_rolloff": "Spectral Rolloff",
    "WOSS": "WOSS (Weighted-Optimum Spectral Shape)",
    "norm_fdom": "Normalized Dominant Frequency"
}

# GPU Memory Management
GPU_MEMORY_THRESHOLDS = {
    "conservative": 0.6,  # Use 60% of GPU memory
    "aggressive": 0.8,    # Use 80% of GPU memory  
    "maximum": 0.9        # Use 90% of GPU memory
}
```

### Phase 1.3: GPU-Aware Session State

**File:** `common/session_state.py`

```python
# common/session_state.py
import streamlit as st
import tempfile
import logging
from .constants import GPU_PROCESSING_PREFERRED

def initialize_session_state():
    """Initialize session state with GPU processing preferences."""
    
    # GPU Processing State
    if 'gpu_available' not in st.session_state:
        st.session_state.gpu_available = False
    if 'gpu_backend' not in st.session_state:
        st.session_state.gpu_backend = "Unknown"
    if 'processing_mode' not in st.session_state:
        st.session_state.processing_mode = "GPU" if GPU_PROCESSING_PREFERRED else "CPU"
    
    # Analysis Mode State
    if 'selected_analysis_mode' not in st.session_state:
        st.session_state.selected_analysis_mode = None
    if 'gpu_batch_size' not in st.session_state:
        st.session_state.gpu_batch_size = 512
        
    # Data Loading State
    if 'header_loader' not in st.session_state:
        st.session_state.header_loader = None
    if 'segy_file_path' not in st.session_state:
        st.session_state.segy_file_path = None
        
    # Processing Results State  
    if 'gpu_processing_results' not in st.session_state:
        st.session_state.gpu_processing_results = None
    if 'processing_backend_used' not in st.session_state:
        st.session_state.processing_backend_used = None
        
    # Export State
    if 'export_config' not in st.session_state:
        st.session_state.export_config = {}
    if 'export_results' not in st.session_state:
        st.session_state.export_results = None

def reset_state():
    """Reset session state for new analysis while preserving GPU info."""
    gpu_info = {
        'gpu_available': st.session_state.get('gpu_available', False),
        'gpu_backend': st.session_state.get('gpu_backend', "Unknown")
    }
    
    # Clear all session state
    for key in list(st.session_state.keys()):
        if key not in ['gpu_available', 'gpu_backend']:
            del st.session_state[key]
    
    # Restore GPU info and reinitialize
    st.session_state.update(gpu_info)
    initialize_session_state()
    
    logging.info("Session state reset - GPU preferences preserved")
```

---

## Phase 2: GPU-Prioritized Processing Architecture

### Phase 2.1: Enhanced GPU Utilities 

**File:** `utils/gpu_utils.py` (enhanced version)

```python
# utils/gpu_utils.py  
import logging
import streamlit as st
import numpy as np

# Global GPU state
GPU_AVAILABLE = False
GPU_DEVICE_INFO = None

@st.cache_resource
def initialize_gpu_system():
    """Initialize and verify GPU processing capability."""
    global GPU_AVAILABLE, GPU_DEVICE_INFO
    
    try:
        import cupy as cp
        
        # Test GPU functionality with realistic workload
        test_size = 1000
        test_data = cp.random.random((test_size,), dtype=cp.float32)
        test_fft = cp.fft.fft(test_data)
        test_result = cp.abs(test_fft)
        
        # Get device information
        device = cp.cuda.Device()
        GPU_DEVICE_INFO = {
            'name': device.attributes['name'],
            'compute_capability': device.compute_capability,
            'memory_total': device.mem_info[1],
            'memory_free': device.mem_info[0]
        }
        
        GPU_AVAILABLE = True
        logging.info(f"GPU processing enabled: {GPU_DEVICE_INFO['name']}")
        return True, GPU_DEVICE_INFO
        
    except ImportError:
        logging.warning("CuPy not available - CPU fallback enabled")
        return False, None
    except Exception as e:
        logging.error(f"GPU initialization failed: {e}")
        return False, None

def get_optimal_processing_config(analysis_mode, num_traces):
    """Get optimal processing configuration for the analysis mode."""
    from .constants import GPU_BATCH_SIZES, CPU_FALLBACK_BATCH_SIZES
    
    if GPU_AVAILABLE:
        batch_size = GPU_BATCH_SIZES.get(analysis_mode, 512)
        processing_backend = "GPU"
        
        # Adjust batch size based on available memory
        if GPU_DEVICE_INFO:
            memory_gb = GPU_DEVICE_INFO['memory_total'] / (1024**3)
            if memory_gb < 4:  # Low memory GPU
                batch_size = min(batch_size // 2, 256)
            elif memory_gb > 16:  # High memory GPU
                batch_size = min(batch_size * 2, 2048)
    else:
        batch_size = CPU_FALLBACK_BATCH_SIZES.get(analysis_mode, 32)
        processing_backend = "CPU"
    
    return {
        'backend': processing_backend,
        'batch_size': batch_size,
        'estimated_batches': (num_traces + batch_size - 1) // batch_size,
        'memory_usage': 'optimized'
    }

def clear_gpu_memory():
    """Clear GPU memory pools and force garbage collection."""
    if GPU_AVAILABLE:
        try:
            import cupy as cp
            mempool = cp.get_default_memory_pool()
            pinned_mempool = cp.get_default_pinned_memory_pool()
            mempool.free_all_blocks()
            pinned_mempool.free_all_blocks()
            logging.info("GPU memory cleared")
        except Exception as e:
            logging.warning(f"Failed to clear GPU memory: {e}")

def get_processing_functions():
    """Get the appropriate processing functions based on GPU availability."""
    if GPU_AVAILABLE:
        try:
            from .dlogst_spec_descriptor_gpu import (
                dlogst_spec_descriptor_gpu,
                dlogst_spec_descriptor_gpu_2d_chunked
            )
            return {
                'single': dlogst_spec_descriptor_gpu,
                'batch': dlogst_spec_descriptor_gpu_2d_chunked,
                'backend': 'GPU'
            }
        except ImportError:
            pass
    
    # Fallback to CPU
    from .dlogst_spec_descriptor_cpu import dlogst_spec_descriptor_cpu
    return {
        'single': dlogst_spec_descriptor_cpu,
        'batch': None,  # CPU processes single traces
        'backend': 'CPU'
    }
```
### Phase 2.2: Mode-Specific GPU Processing

**File:** `utils/processing_gpu.py` 

```python
# utils/processing_gpu.py
import numpy as np
import logging
from tqdm import tqdm
from .gpu_utils import get_processing_functions, get_optimal_processing_config, clear_gpu_memory
from .data_utils import load_trace_sample

def process_inline_analysis_gpu(segy_path, inline_number, header_loader, dt, plot_settings):
    """GPU-optimized processing for single inline analysis."""
    logging.info(f"Starting GPU-optimized inline {inline_number} analysis")
    
    # Filter traces for the selected inline
    inline_mask = header_loader.inlines == inline_number
    trace_indices = header_loader.unique_indices[inline_mask]
    crosslines = header_loader.crosslines[inline_mask]
    
    # Get optimal processing configuration
    config = get_optimal_processing_config("Single inline (all crosslines)", len(trace_indices))
    
    # Process with GPU prioritization
    trace_data, descriptors = process_traces_gpu_batch(
        segy_path, trace_indices, dt, plot_settings, config
    )
    
    return {
        'trace_data': trace_data,
        'descriptors': descriptors,
        'crosslines': crosslines,
        'processing_config': config
    }

def process_crossline_analysis_gpu(segy_path, crossline_number, header_loader, dt, plot_settings):
    """GPU-optimized processing for single crossline analysis."""
    logging.info(f"Starting GPU-optimized crossline {crossline_number} analysis")
    
    # Filter traces for the selected crossline
    crossline_mask = header_loader.crosslines == crossline_number
    trace_indices = header_loader.unique_indices[crossline_mask]
    inlines = header_loader.inlines[crossline_mask]
    
    # Get optimal processing configuration
    config = get_optimal_processing_config("Single crossline (all inlines)", len(trace_indices))
    
    # Process with GPU prioritization
    trace_data, descriptors = process_traces_gpu_batch(
        segy_path, trace_indices, dt, plot_settings, config
    )
    
    return {
        'trace_data': trace_data,
        'descriptors': descriptors,
        'inlines': inlines,
        'processing_config': config
    }

def process_aoi_analysis_gpu(segy_path, inline_range, crossline_range, header_loader, dt, plot_settings):
    """GPU-optimized processing for Area of Interest (AOI) analysis."""
    logging.info(f"Starting GPU-optimized AOI analysis: IL{inline_range[0]}-{inline_range[1]}, XL{crossline_range[0]}-{crossline_range[1]}")
    
    # Filter traces within AOI bounds
    inline_mask = (header_loader.inlines >= inline_range[0]) & (header_loader.inlines <= inline_range[1])
    crossline_mask = (header_loader.crosslines >= crossline_range[0]) & (header_loader.crosslines <= crossline_range[1])
    aoi_mask = inline_mask & crossline_mask
    
    trace_indices = header_loader.unique_indices[aoi_mask]
    inlines = header_loader.inlines[aoi_mask]
    crosslines = header_loader.crosslines[aoi_mask]
    
    # Get optimal processing configuration for AOI
    config = get_optimal_processing_config("By inline/crossline section (AOI)", len(trace_indices))
    
    # Process with GPU prioritization
    trace_data, descriptors = process_traces_gpu_batch(
        segy_path, trace_indices, dt, plot_settings, config
    )
    
    return {
        'trace_data': trace_data,
        'descriptors': descriptors,
        'inlines': inlines,
        'crosslines': crosslines,
        'aoi_bounds': {'inline_range': inline_range, 'crossline_range': crossline_range},
        'processing_config': config
    }

def process_polyline_analysis_gpu(segy_path, polyline_indices, header_loader, dt, plot_settings):
    """GPU-optimized processing for polyline analysis."""
    logging.info(f"Starting GPU-optimized polyline analysis with {len(polyline_indices)} traces")
    
    # Get coordinates for polyline traces
    inlines = header_loader.inlines[polyline_indices]
    crosslines = header_loader.crosslines[polyline_indices]
    x_coords = header_loader.x_coords[polyline_indices]
    y_coords = header_loader.y_coords[polyline_indices]
    
    # Get optimal processing configuration for polyline
    config = get_optimal_processing_config("By Polyline File Import", len(polyline_indices))
    
    # Process with GPU prioritization
    trace_data, descriptors = process_traces_gpu_batch(
        segy_path, header_loader.unique_indices[polyline_indices], dt, plot_settings, config
    )
    
    return {
        'trace_data': trace_data,
        'descriptors': descriptors,
        'inlines': inlines,
        'crosslines': crosslines,
        'x_coords': x_coords,
        'y_coords': y_coords,
        'processing_config': config
    }

def process_traces_gpu_batch(segy_path, trace_indices, dt, plot_settings, config):
    """Core GPU batch processing function for all analysis modes."""
    processing_funcs = get_processing_functions()
    backend = processing_funcs['backend']
    batch_size = config['batch_size']
    
    logging.info(f"Processing {len(trace_indices)} traces using {backend} backend (batch size: {batch_size})")
    
    if backend == 'GPU' and processing_funcs['batch'] is not None:
        return _process_gpu_batched(segy_path, trace_indices, dt, plot_settings, processing_funcs, batch_size)
    else:
        return _process_cpu_sequential(segy_path, trace_indices, dt, plot_settings, processing_funcs, batch_size)

def _process_gpu_batched(segy_path, trace_indices, dt, plot_settings, processing_funcs, batch_size):
    """GPU-optimized batch processing using CuPy."""
    import cupy as cp
    
    all_trace_data = []
    all_descriptors = []
    
    # Process in GPU-optimized batches
    for i in tqdm(range(0, len(trace_indices), batch_size), desc="GPU Batch Processing"):
        batch_indices = trace_indices[i:i + batch_size]
        
        try:
            # Load batch traces
            batch_traces = []
            for idx in batch_indices:
                trace = load_trace_sample(segy_path, idx)
                batch_traces.append(trace)
            
            # Convert to GPU arrays for batch processing
            gpu_traces = cp.array(batch_traces, dtype=cp.float32)
            
            # Process entire batch on GPU
            batch_descriptors = processing_funcs['batch'](gpu_traces, dt, **plot_settings)
            
            # Convert results back to CPU
            cpu_traces = [cp.asnumpy(trace) for trace in gpu_traces]
            cpu_descriptors = {}
            for key, value in batch_descriptors.items():
                if hasattr(value, 'get'):  # CuPy array
                    cpu_descriptors[key] = cp.asnumpy(value)
                else:
                    cpu_descriptors[key] = value
            
            all_trace_data.extend(cpu_traces)
            all_descriptors.append(cpu_descriptors)
            
            # Clear GPU memory
            del gpu_traces, batch_descriptors
            
        except Exception as e:
            logging.error(f"GPU batch processing failed: {e}")
            # Fallback to CPU for this batch
            for idx in batch_indices:
                trace = load_trace_sample(segy_path, idx)
                descriptors = processing_funcs['single'](trace, dt, **plot_settings)
                all_trace_data.append(trace)
                all_descriptors.append(descriptors)
        
        # Clear GPU memory after each batch
        clear_gpu_memory()
    
    return all_trace_data, all_descriptors

def _process_cpu_sequential(segy_path, trace_indices, dt, plot_settings, processing_funcs, batch_size):
    """CPU fallback processing."""
    all_trace_data = []
    all_descriptors = []
    
    # Use smaller batches for CPU to manage memory
    cpu_batch_size = min(batch_size // 4, 32)
    
    for i in tqdm(range(0, len(trace_indices), cpu_batch_size), desc="CPU Processing"):
        batch_indices = trace_indices[i:i + cpu_batch_size]
        
        for idx in batch_indices:
            trace = load_trace_sample(segy_path, idx)
            descriptors = processing_funcs['single'](trace, dt, **plot_settings)
            
            all_trace_data.append(trace)
            all_descriptors.append(descriptors)
    
    return all_trace_data, all_descriptors
```

---

## Phase 3: GPU-Optimized Page Modules

### Phase 3.1: Analysis Mode Selection Page

**File:** `pages/3_select_area.py`

```python
# pages/3_select_area.py
import streamlit as st
import numpy as np
import pandas as pd
from common.constants import ANALYSIS_MODES
from common.session_state import initialize_session_state
from utils.gpu_utils import get_optimal_processing_config
from utils.general_utils import find_traces_near_polyline, parse_polyline_string
import logging

st.set_page_config(page_title="Select Analysis Area", layout="wide")
st.title("Step 3: Select Analysis Area (GPU-Optimized)")

# Initialize session state
initialize_session_state()

# Check for loaded data
if not st.session_state.get('header_loader'):
    st.error("❌ No data loaded. Please go to Step 1 to load your SEG-Y file.")
    st.stop()

# Display GPU status
if st.session_state.get('gpu_available'):
    st.success("🚀 GPU acceleration enabled for analysis")
else:
    st.warning("💻 Using CPU processing (GPU not available)")

header_loader = st.session_state.header_loader

# Sidebar - Analysis mode selection
st.sidebar.header("🎯 Analysis Mode Selection")
selected_mode = st.sidebar.selectbox(
    "Select analysis mode:",
    ANALYSIS_MODES,
    key="analysis_mode_selector"
)

# Store selected mode
st.session_state.selected_analysis_mode = selected_mode

# Get estimated processing configuration
if selected_mode:
    # Estimate number of traces for this mode
    if "Single inline" in selected_mode:
        unique_inlines = np.unique(header_loader.inlines)
        est_traces = len(header_loader.inlines) // len(unique_inlines)
    elif "Single crossline" in selected_mode:
        unique_crosslines = np.unique(header_loader.crosslines)
        est_traces = len(header_loader.crosslines) // len(unique_crosslines)
    else:
        est_traces = len(header_loader.unique_indices)
    
    config = get_optimal_processing_config(selected_mode, est_traces)
    
    # Display processing info
    with st.sidebar.expander("🔧 Processing Configuration"):
        st.write(f"**Backend:** {config['backend']}")
        st.write(f"**Optimal batch size:** {config['batch_size']}")
        st.write(f"**Estimated batches:** {config['estimated_batches']}")

# Main content - Mode-specific UI
st.markdown("---")

if "Single inline" in selected_mode:
    render_inline_selection()
elif "Single crossline" in selected_mode:
    render_crossline_selection()  
elif "AOI" in selected_mode:
    render_aoi_selection()
elif "Polyline" in selected_mode:
    render_polyline_selection()
elif "well markers" in selected_mode:
    render_well_marker_selection()

def render_inline_selection():
    """Render single inline selection interface."""
    st.subheader("🔗 Single Inline Analysis (GPU-Optimized)")
    
    # Get available inlines
    unique_inlines = np.unique(header_loader.inlines)
    inline_range = f"{unique_inlines.min()} - {unique_inlines.max()}"
    
    col1, col2 = st.columns([2, 1])
    
    with col1:
        st.info(f"📊 Available inlines: {inline_range} ({len(unique_inlines)} unique inlines)")
        
        selected_inline = st.selectbox(
            "Select inline number:",
            unique_inlines,
            help="Choose an inline for GPU-accelerated analysis of all crosslines"
        )
        
        # Show trace count for this inline
        inline_mask = header_loader.inlines == selected_inline
        trace_count = np.sum(inline_mask)
        crosslines = header_loader.crosslines[inline_mask]
        crossline_range = f"{crosslines.min()} - {crosslines.max()}"
        
        st.write(f"**Selected inline {selected_inline}:**")
        st.write(f"• {trace_count} traces")
        st.write(f"• Crosslines: {crossline_range}")
        
    with col2:
        # Show GPU optimization info
        config = get_optimal_processing_config(selected_mode, trace_count)
        st.markdown("### 🚀 GPU Optimization")
        st.metric("Processing Backend", config['backend'])
        st.metric("Batch Size", config['batch_size'])
        st.metric("Est. Processing Time", f"{config['estimated_batches']} batches")
    
    # Process button
    if st.button("🚀 Start GPU-Accelerated Analysis", type="primary", use_container_width=True):
        st.session_state.selected_inline = selected_inline
        st.session_state.area_selected = True
        st.session_state.analysis_mode = selected_mode
        st.success(f"✅ Inline {selected_inline} selected for GPU processing!")
        st.info("👉 Navigate to **Step 4** to start the analysis")

def render_crossline_selection():
    """Render single crossline selection interface."""
    st.subheader("📏 Single Crossline Analysis (GPU-Optimized)")
    
    unique_crosslines = np.unique(header_loader.crosslines)
    crossline_range = f"{unique_crosslines.min()} - {unique_crosslines.max()}"
    
    col1, col2 = st.columns([2, 1])
    
    with col1:
        st.info(f"📊 Available crosslines: {crossline_range} ({len(unique_crosslines)} unique crosslines)")
        
        selected_crossline = st.selectbox(
            "Select crossline number:",
            unique_crosslines,
            help="Choose a crossline for GPU-accelerated analysis of all inlines"
        )
        
        # Show trace count for this crossline
        crossline_mask = header_loader.crosslines == selected_crossline
        trace_count = np.sum(crossline_mask)
        inlines = header_loader.inlines[crossline_mask]
        inline_range = f"{inlines.min()} - {inlines.max()}"
        
        st.write(f"**Selected crossline {selected_crossline}:**")
        st.write(f"• {trace_count} traces")
        st.write(f"• Inlines: {inline_range}")
        
    with col2:
        # Show GPU optimization info
        config = get_optimal_processing_config(selected_mode, trace_count)
        st.markdown("### 🚀 GPU Optimization")
        st.metric("Processing Backend", config['backend'])
        st.metric("Batch Size", config['batch_size'])
        st.metric("Est. Processing Time", f"{config['estimated_batches']} batches")
    
    if st.button("🚀 Start GPU-Accelerated Analysis", type="primary", use_container_width=True):
        st.session_state.selected_crossline = selected_crossline
        st.session_state.area_selected = True
        st.session_state.analysis_mode = selected_mode
        st.success(f"✅ Crossline {selected_crossline} selected for GPU processing!")
        st.info("👉 Navigate to **Step 4** to start the analysis")

def render_aoi_selection():
    """Render Area of Interest (AOI) selection interface."""
    st.subheader("🎯 Area of Interest (AOI) Analysis (GPU-Optimized)")
    
    # Get data ranges
    inline_min, inline_max = int(header_loader.inlines.min()), int(header_loader.inlines.max())
    crossline_min, crossline_max = int(header_loader.crosslines.min()), int(header_loader.crosslines.max())
    
    col1, col2 = st.columns([2, 1])
    
    with col1:
        st.info(f"📊 Survey extent: IL {inline_min}-{inline_max}, XL {crossline_min}-{crossline_max}")
        
        # AOI selection
        st.markdown("#### Define AOI Bounds:")
        
        col_il1, col_il2 = st.columns(2)
        with col_il1:
            aoi_inline_min = st.number_input("Inline Min", value=inline_min, min_value=inline_min, max_value=inline_max)
        with col_il2:
            aoi_inline_max = st.number_input("Inline Max", value=inline_max, min_value=inline_min, max_value=inline_max)
            
        col_xl1, col_xl2 = st.columns(2)
        with col_xl1:
            aoi_crossline_min = st.number_input("Crossline Min", value=crossline_min, min_value=crossline_min, max_value=crossline_max)
        with col_xl2:
            aoi_crossline_max = st.number_input("Crossline Max", value=crossline_max, min_value=crossline_min, max_value=crossline_max)
        
        # Calculate AOI stats
        inline_mask = (header_loader.inlines >= aoi_inline_min) & (header_loader.inlines <= aoi_inline_max)
        crossline_mask = (header_loader.crosslines >= aoi_crossline_min) & (header_loader.crosslines <= aoi_crossline_max)
        aoi_mask = inline_mask & crossline_mask
        aoi_trace_count = np.sum(aoi_mask)
        
        st.write(f"**AOI Coverage:**")
        st.write(f"• IL {aoi_inline_min}-{aoi_inline_max}, XL {aoi_crossline_min}-{aoi_crossline_max}")
        st.write(f"• {aoi_trace_count} traces selected")
        
    with col2:
        # Show GPU optimization info for AOI
        config = get_optimal_processing_config(selected_mode, aoi_trace_count)
        st.markdown("### 🚀 GPU Optimization")
        st.metric("Processing Backend", config['backend'])
        st.metric("Batch Size", config['batch_size'])
        st.metric("Est. Processing Time", f"{config['estimated_batches']} batches")
        
        # Memory estimation
        if aoi_trace_count > 0:
            est_memory_gb = (aoi_trace_count * 1000 * 4) / (1024**3)  # Rough estimate
            st.metric("Est. Memory Usage", f"{est_memory_gb:.1f} GB")
    
    if aoi_trace_count > 0:
        if st.button("🚀 Start GPU-Accelerated AOI Analysis", type="primary", use_container_width=True):
            st.session_state.aoi_bounds = {
                'inline_min': aoi_inline_min, 'inline_max': aoi_inline_max,
                'crossline_min': aoi_crossline_min, 'crossline_max': aoi_crossline_max
            }
            st.session_state.area_selected = True
            st.session_state.analysis_mode = selected_mode
            st.success(f"✅ AOI selected: {aoi_trace_count} traces for GPU processing!")
            st.info("👉 Navigate to **Step 4** to start the analysis")
    else:
        st.warning("⚠️ No traces found in the selected AOI bounds")

def render_polyline_selection():
    """Render polyline analysis selection interface."""
    st.subheader("📍 Polyline Analysis (GPU-Optimized)")
    
    col1, col2 = st.columns([2, 1])
    
    with col1:
        st.markdown("#### Define Polyline Path:")
        
        # Polyline input methods
        input_method = st.radio(
            "Input method:",
            ["Manual coordinates", "Upload CSV file"],
            help="Choose how to specify the polyline path"
        )
        
        if input_method == "Manual coordinates":
            polyline_text = st.text_area(
                "Enter polyline coordinates (X,Y pairs):",
                placeholder="x1,y1\nx2,y2\nx3,y3\n...",
                help="Enter X,Y coordinate pairs, one per line"
            )
            
            tolerance = st.number_input(
                "Tolerance (distance from polyline):",
                value=100.0, min_value=1.0, max_value=1000.0,
                help="Maximum distance from polyline to include traces"
            )
            
            if polyline_text:
                try:
                    polyline_points = parse_polyline_string(polyline_text)
                    polyline_indices = find_traces_near_polyline(header_loader, polyline_points, tolerance)
                    
                    st.write(f"**Polyline Analysis:**")
                    st.write(f"• {len(polyline_points)} polyline vertices")
                    st.write(f"• {len(polyline_indices)} traces within {tolerance}m")
                    
                    if len(polyline_indices) > 0:
                        st.session_state.polyline_indices = polyline_indices
                        
                except Exception as e:
                    st.error(f"Error parsing polyline: {e}")
                    polyline_indices = []
            else:
                polyline_indices = []
                
        else:  # CSV upload
            uploaded_file = st.file_uploader(
                "Upload polyline CSV file",
                type=['csv'],
                help="CSV file with X,Y columns"
            )
            
            if uploaded_file:
                try:
                    df = pd.read_csv(uploaded_file)
                    if 'X' in df.columns and 'Y' in df.columns:
                        polyline_points = list(zip(df['X'], df['Y']))
                        
                        tolerance = st.number_input(
                            "Tolerance (distance from polyline):",
                            value=100.0, min_value=1.0, max_value=1000.0
                        )
                        
                        polyline_indices = find_traces_near_polyline(header_loader, polyline_points, tolerance)
                        
                        st.write(f"**Polyline Analysis:**")
                        st.write(f"• {len(polyline_points)} polyline vertices")
                        st.write(f"• {len(polyline_indices)} traces within {tolerance}m")
                        
                        if len(polyline_indices) > 0:
                            st.session_state.polyline_indices = polyline_indices
                    else:
                        st.error("CSV file must contain 'X' and 'Y' columns")
                        polyline_indices = []
                except Exception as e:
                    st.error(f"Error reading CSV file: {e}")
                    polyline_indices = []
            else:
                polyline_indices = []
    
    with col2:
        # Show GPU optimization info for polyline
        if 'polyline_indices' in st.session_state:
            trace_count = len(st.session_state.polyline_indices)
            config = get_optimal_processing_config(selected_mode, trace_count)
            
            st.markdown("### 🚀 GPU Optimization")
            st.metric("Processing Backend", config['backend'])
            st.metric("Batch Size", config['batch_size'])
            st.metric("Est. Processing Time", f"{config['estimated_batches']} batches")
    
    if st.session_state.get('polyline_indices') and len(st.session_state.polyline_indices) > 0:
        if st.button("🚀 Start GPU-Accelerated Polyline Analysis", type="primary", use_container_width=True):
            st.session_state.area_selected = True
            st.session_state.analysis_mode = selected_mode
            st.success(f"✅ Polyline selected: {len(st.session_state.polyline_indices)} traces for GPU processing!")
            st.info("👉 Navigate to **Step 4** to start the analysis")

def render_well_marker_selection():
    """Render well marker selection interface."""
    st.subheader("🎯 Well Marker Analysis (GPU-Optimized)")
    
    if not st.session_state.get('well_data'):
        st.warning("⚠️ No well data loaded. Please load well data in Step 1.")
        return
    
    well_data = st.session_state.well_data
    
    col1, col2 = st.columns([2, 1])
    
    with col1:
        st.info(f"📊 Available wells: {len(well_data)} wells loaded")
        
        # Well marker selection
        available_markers = well_data.columns.tolist()
        selected_markers = st.multiselect(
            "Select markers for analysis:",
            available_markers,
            help="Choose which markers to analyze with GPU acceleration"
        )
        
        if selected_markers:
            # Count traces for selected markers
            valid_wells = well_data.dropna(subset=selected_markers)
            trace_count = len(valid_wells) * len(selected_markers)
            
            st.write(f"**Analysis Summary:**")
            st.write(f"• {len(selected_markers)} markers selected")
            st.write(f"• {len(valid_wells)} valid wells")
            st.write(f"• {trace_count} total analyses")
            
    with col2:
        if selected_markers:
            config = get_optimal_processing_config(selected_mode, trace_count)
            
            st.markdown("### 🚀 GPU Optimization")
            st.metric("Processing Backend", config['backend'])
            st.metric("Batch Size", config['batch_size'])
            st.metric("Est. Processing Time", f"{config['estimated_batches']} batches")
    
    if selected_markers:
        if st.button("🚀 Start GPU-Accelerated Well Analysis", type="primary", use_container_width=True):
            st.session_state.selected_markers = selected_markers
            st.session_state.area_selected = True
            st.session_state.analysis_mode = selected_mode
            st.success(f"✅ {len(selected_markers)} markers selected for GPU processing!")
            st.info("👉 Navigate to **Step 4** to start the analysis")
```
    expected_cols = ["X", "Y", "Z", "MD", "Surface", "Well"]
    for col in expected_cols:
        if col not in df.columns:
            raise ValueError(f"Missing expected column '{col}' in Excel file.")
    return df

def get_nearest_trace_index(header_loader, well_x, well_y):
    coords = np.column_stack((header_loader.x_coords, header_loader.y_coords))
    distances = np.linalg.norm(coords - np.array([well_x, well_y]), axis=1)
    return header_loader.unique_indices[np.argmin(distances)]

def load_trace_sample(segy_path, trace_index):
    try:
        with segyio.open(segy_path, 'r', ignore_geometry=True) as segyfile:
            if trace_index < 0 or trace_index >= segyfile.tracecount:
                logging.warning(f"Trace index {trace_index} is out of bounds.")
                return None
            return segyfile.trace.raw[trace_index]
    except Exception as e:
        logging.error(f"Failed to load trace {trace_index} from {segy_path}: {e}")
        return None

def get_sampling_interval(segy_path):
    try:
        with segyio.open(segy_path, 'r', ignore_geometry=True) as segyfile:
            return segyio.tools.dt(segyfile) / 1000.0  # Return in ms
    except Exception as e:
        logging.error(f"Could not read sampling interval from {segy_path}: {e}")
        return None

def get_trace_count(segy_path):
    try:
        with segyio.open(segy_path, 'r', ignore_geometry=True) as segyfile:
            return segyfile.tracecount
    except Exception as e:
        logging.error(f"Could not read trace count from {segy_path}: {e}")
        return 0
```

### Step 3.2: `utils/processing.py`

This file contains the core scientific computations, completely separate from any UI.

**Action:** Overwrite the existing `utils/processing.py` with the following `tkinter`-free version:

```python
# utils/processing.py
import numpy as np
import segyio
import logging
from tqdm import tqdm
from .data_utils import load_trace_sample

def calculate_woss(descriptor, plot_settings):
    """
    Calculate WOSS (Weighted-Optimum Spectral Shape) from spectral descriptors.
    
    Args:
        descriptor (dict): Dictionary containing spectral descriptors
        plot_settings (dict): Settings containing epsilon, fdom_exponent, and hfc_p95
        
    Returns:
        np.ndarray: WOSS values
    """
    epsilon = plot_settings.get('epsilon', 1e-4)
    fdom_exponent = plot_settings.get('fdom_exponent', 2.0)
    hfc_p95 = plot_settings.get('hfc_p95', 1.0)
    
    required_keys = ['hfc', 'norm_fdom', 'mag_voice_slope']
    if not all(key in descriptor for key in required_keys):
        logging.warning(f"Missing required keys for WOSS calculation: {required_keys}")
        return np.array([], dtype=np.float32)
    
    # Normalize HFC
    hfc_norm = descriptor['hfc'] / hfc_p95 if hfc_p95 != 0 else descriptor['hfc']
    
    # Calculate denominator
    denominator = hfc_norm * (descriptor['norm_fdom']**fdom_exponent + epsilon)
    
    # Calculate WOSS with error handling
    with np.errstate(divide='ignore', invalid='ignore'):
        woss = np.where(denominator > epsilon, descriptor['mag_voice_slope'] / denominator, 0.0)
        woss[~np.isfinite(woss)] = 0.0
        woss = np.clip(woss, -1e6, 1e6)
    
    return woss.astype(np.float32)

def clean_array(arr):
    """
    Clean an array by removing invalid values and converting to real numbers.
    
    Args:
        arr: Input array
        
    Returns:
        np.ndarray: Cleaned array
    """
    if not isinstance(arr, np.ndarray) or arr.size == 0:
        return np.array([])
    
    if np.iscomplexobj(arr):
        arr = np.real(arr)
    
    return arr[np.isfinite(arr)]

def calculate_stats_and_defaults(segy_path, dt, sample_percent, max_traces_for_stats, 
                                spectral_params, spec_descriptor_func):
    """
    Calculate statistics and default plotting limits from a sample of traces.
    
    Args:
        segy_path (str): Path to SEG-Y file
        dt (float): Sampling interval
        sample_percent (float): Percentage of traces to sample
        max_traces_for_stats (int): Maximum number of traces for statistics
        spectral_params (dict): Parameters for spectral analysis
        spec_descriptor_func (callable): Function to calculate spectral descriptors
        
    Returns:
        dict: Dictionary containing statistics and default limits
    """
    try:
        with segyio.open(segy_path, 'r', ignore_geometry=True) as segyfile:
            num_traces_total = segyfile.tracecount
            
        if num_traces_total == 0:
            logging.warning("SEG-Y file has 0 traces.")
            return None
            
        # Calculate number of traces to sample
        num_traces_to_sample = min(
            max(1, int(num_traces_total * (sample_percent / 100.0))),
            max_traces_for_stats
        )
        num_traces_to_sample = min(num_traces_to_sample, num_traces_total)
        
        # Select random sample of traces
        sample_indices = np.random.choice(num_traces_total, num_traces_to_sample, replace=False)
        
        # Load sample traces
        sample_descriptors = []
        loaded_samples = []
        
        for i in tqdm(sample_indices, desc="Loading sample traces"):
            trace_sample = load_trace_sample(segy_path, i)
            if trace_sample is not None and trace_sample.size > 0:
                loaded_samples.append(trace_sample)
        
        if not loaded_samples:
            logging.error("No valid sample traces could be loaded.")
            return None
        
        # Pad traces to same length
        max_len = max(len(s) for s in loaded_samples)
        
        # Calculate descriptors for each sample
        for trace_sample in tqdm(loaded_samples, desc="Calculating sample descriptors"):
            try:
                if len(trace_sample) < max_len:
                    trace_sample = np.pad(trace_sample, (0, max_len - len(trace_sample)), 'constant')
                
                fmax_calc = (1 / (dt * 2)) * 0.8  # 80% of Nyquist
                descriptor = spec_descriptor_func(trace_sample, dt, fmax=fmax_calc, **spectral_params)
                descriptor['data'] = trace_sample
                sample_descriptors.append(descriptor)
                
            except Exception as e:
                logging.warning(f"Error calculating descriptor for a sample trace: {e}")

        if not sample_descriptors:
            logging.error("Failed to calculate descriptors for any sample traces.")
            return None

        # Calculate statistics
        stats_results = calculate_descriptor_statistics(sample_descriptors)
        percentile_defaults = calculate_percentile_defaults(sample_descriptors, spectral_params)
        
        return {
            'stats': stats_results,
            'defaults': percentile_defaults
        }

    except Exception as e:
        logging.error(f"Error in calculate_stats_and_defaults: {e}")
        return None

def calculate_descriptor_statistics(sample_descriptors):
    """
    Calculate comprehensive statistics for all descriptors.
    
    Args:
        sample_descriptors (list): List of descriptor dictionaries
        
    Returns:
        dict: Statistics for each descriptor
    """
    stats_results = {}
    
    # Define keys to analyze
    stat_keys = ["data", "hfc", "spec_decrease", "spec_slope", "mag_voice_slope", 
                 "spec_bandwidth", "spec_rolloff", "norm_fdom"]
    
    for key in stat_keys:
        # Collect all values for this descriptor
        arr_list = []
        for descriptor in sample_descriptors:
            if key in descriptor and isinstance(descriptor[key], np.ndarray):
                arr_list.append(descriptor[key].flatten())
        
        if arr_list:
            combined_arr = clean_array(np.concatenate(arr_list))
            
            if combined_arr.size > 0:
                stats_results[key] = {
                    'mean': float(np.mean(combined_arr)),
                    'std': float(np.std(combined_arr)),
                    'min': float(np.min(combined_arr)),
                    'max': float(np.max(combined_arr)),
                    'p5': float(np.percentile(combined_arr, 5)),
                    'p25': float(np.percentile(combined_arr, 25)),
                    'p50': float(np.percentile(combined_arr, 50)),
                    'p75': float(np.percentile(combined_arr, 75)),
                    'p95': float(np.percentile(combined_arr, 95)),
                    'count': len(combined_arr)
                }
    
    return stats_results

def calculate_percentile_defaults(sample_descriptors, spectral_params):
    """
    Calculate default percentile values for plotting limits.
    
    Args:
        sample_descriptors (list): List of descriptor dictionaries
        spectral_params (dict): Spectral analysis parameters
        
    Returns:
        dict: Default percentile values
    """
    percentile_defaults = {}
    data_arrays = {}
    
    # Define keys to analyze
    stat_keys = ["data", "hfc", "spec_decrease", "spec_slope", "mag_voice_slope", 
                 "spec_bandwidth", "spec_rolloff", "norm_fdom"]
    
    # Collect arrays for each descriptor
    for key in stat_keys:
        arr_list = []
        for descriptor in sample_descriptors:
            if key in descriptor and isinstance(descriptor[key], np.ndarray):
                arr_list.append(descriptor[key].flatten())
        
        if arr_list:
            data_arrays[key] = clean_array(np.concatenate(arr_list))

    # Calculate percentiles
    for key, arr in data_arrays.items():
        if arr.size > 0:
            p5, p95 = np.percentile(arr, [5, 95])
            percentile_defaults[f"{key}_p5"] = float(p5)
            percentile_defaults[f"{key}_p95"] = float(p95)
            
            # Calculate absolute percentiles for certain descriptors
            if key in ["data", "mag_voice_slope"]:
                percentile_defaults[f"{key}_p95_abs"] = float(np.percentile(np.abs(arr), 95))

    # Calculate WOSS percentiles separately
    woss_settings = {**spectral_params, 'hfc_p95': percentile_defaults.get('hfc_p95', 1.0)}
    woss_values = []
    
    for descriptor in sample_descriptors:
        try:
            woss = calculate_woss(descriptor, woss_settings)
            if woss.size > 0:
                woss_values.append(woss)
        except Exception as e:
            logging.warning(f"Error calculating WOSS for sample: {e}")
    
    if woss_values:
        woss_combined = clean_array(np.concatenate(woss_values))
        if woss_combined.size > 0:
            p5, p95 = np.percentile(woss_combined, [5, 95])
            percentile_defaults['woss_p5'] = float(p5)
            percentile_defaults['woss_p95'] = float(p95)
            percentile_defaults['woss_p95_abs'] = float(np.percentile(np.abs(woss_combined), 95))

    return percentile_defaults

def process_traces_batch(segy_path, trace_indices, dt, spectral_params, spec_descriptor_func, 
                        batch_size=100, progress_callback=None):
    """
    Process multiple traces in batches for memory efficiency.
    
    Args:
        segy_path (str): Path to SEG-Y file
        trace_indices (list): List of trace indices to process
        dt (float): Sampling interval
        spectral_params (dict): Parameters for spectral analysis
        spec_descriptor_func (callable): Function to calculate spectral descriptors
        batch_size (int): Number of traces to process at once
        progress_callback (callable): Optional callback for progress updates
        
    Returns:
        tuple: (trace_data_list, descriptors_list)
    """
    all_trace_data = []
    all_descriptors = []
    
    # Process in batches
    for i in range(0, len(trace_indices), batch_size):
        batch_indices = trace_indices[i:i + batch_size]
        
        # Load batch traces
        batch_traces = []
        for idx in batch_indices:
            trace_sample = load_trace_sample(segy_path, idx)
            if trace_sample is not None:
                batch_traces.append(trace_sample)
        
        if not batch_traces:
            continue
            
        # Find max length for padding
        max_len = max(len(t) for t in batch_traces)
        
        # Process each trace in the batch
        for trace_sample in batch_traces:
            try:
                if len(trace_sample) < max_len:
                    trace_sample = np.pad(trace_sample, (0, max_len - len(trace_sample)), 'constant')
                
                fmax_calc = (1 / (dt * 2)) * 0.8  # 80% of Nyquist
                descriptor = spec_descriptor_func(trace_sample, dt, fmax=fmax_calc, **spectral_params)
                
                all_trace_data.append(trace_sample)
                all_descriptors.append(descriptor)
                
            except Exception as e:
                logging.warning(f"Error processing trace: {e}")
        
        # Update progress if callback provided
        if progress_callback:
            progress = min(1.0, (i + batch_size) / len(trace_indices))
            progress_callback(progress)
    
    return all_trace_data, all_descriptors
```

### Step 3.3: `utils/dlogst_spec_descriptor_cpu.py`

This file contains the CPU implementation of the spectral descriptor calculations as a fallback when GPU is not available.

**Action:** Create a new file `utils/dlogst_spec_descriptor_cpu.py` and add the following code:

```python
# utils/dlogst_spec_descriptor_cpu.py
import numpy as np
from scipy.signal import find_peaks
from scipy.fft import fft, fftfreq
import logging

def dlogst_spec_descriptor_cpu(trace, dt, fmax=None, **kwargs):
    """
    CPU implementation of spectral descriptor calculations.
    
    Args:
        trace: 1D numpy array of seismic trace
        dt: Sampling interval in seconds
        fmax: Maximum frequency for analysis (Hz)
        **kwargs: Additional parameters for spectral analysis
    
    Returns:
        dict: Dictionary containing calculated spectral descriptors
    """
    trace = np.asarray(trace, dtype=np.float32)
    
    # Handle edge cases
    if len(trace) == 0:
        return {}
        
    # Parameters with defaults
    use_band_limited = kwargs.get('use_band_limited', False)
    b1 = kwargs.get('b1', 5.0)
    b2 = kwargs.get('b2', 40.0)
    shape = kwargs.get('shape', 1.0)
    kmax = kwargs.get('kmax', 1.0)
    int_val = kwargs.get('int_val', 1.0)
    p_bandwidth = kwargs.get('p_bandwidth', 0.5)
    roll_percent = kwargs.get('roll_percent', 0.85)
    
    # Calculate spectrum
    spectrum = fft(trace)
    freqs = fftfreq(len(trace), dt)
    
    # Keep only positive frequencies
    n_positive = len(freqs) // 2
    freqs = freqs[:n_positive]
    spectrum = spectrum[:n_positive]
    
    if fmax is not None:
        freq_mask = freqs <= fmax
        freqs = freqs[freq_mask]
        spectrum = spectrum[freq_mask]
    
    # Magnitude spectrum
    magnitude = np.abs(spectrum)
    
    # Avoid division by zero
    magnitude = np.where(magnitude < 1e-10, 1e-10, magnitude)
    
    # Calculate voice (instantaneous frequency)
    voice = np.zeros_like(magnitude)
    if len(magnitude) > 1:
        phase = np.angle(spectrum)
        voice[1:] = np.diff(phase) / (2 * np.pi * dt)
    
    # Logistic transform for voice
    voice_transformed = kmax / (1 + np.exp(-shape * (voice - int_val)))
    
    # Magnitude * Voice
    mag_voice = magnitude * voice_transformed
    
    # Spectral descriptors
    descriptors = {
        'data': trace,
        'magnitude': magnitude,
        'voice': voice_transformed,
        'mag_voice': mag_voice
    }
    
    # Calculate slopes
    if use_band_limited and len(freqs) > 1:
        freq_mask = (freqs >= b1) & (freqs <= b2)
        if np.any(freq_mask):
            f_band = freqs[freq_mask]
            mag_band = magnitude[freq_mask]
            voice_band = voice_transformed[freq_mask]
            
            # Spectral slope
            if len(f_band) > 1:
                descriptors['spec_slope'] = calculate_slope_cpu(f_band, mag_band)
            
            # Mag*Voice slope
            if len(f_band) > 1:
                descriptors['mag_voice_slope'] = calculate_slope_cpu(f_band, mag_band * voice_band)
    else:
        # Full band
        if len(freqs) > 1:
            descriptors['spec_slope'] = calculate_slope_cpu(freqs, magnitude)
            descriptors['mag_voice_slope'] = calculate_slope_cpu(freqs, mag_voice)
    
    # Spectral decrease
    descriptors['spec_decrease'] = calculate_decrease_cpu(magnitude)
    
    # High Frequency Content (HFC)
    descriptors['hfc'] = calculate_hfc_cpu(freqs, magnitude)
    
    # Spectral bandwidth
    descriptors['spec_bandwidth'] = calculate_bandwidth_cpu(freqs, magnitude, p_bandwidth)
    
    # Spectral rolloff
    descriptors['spec_rolloff'] = calculate_rolloff_cpu(freqs, magnitude, roll_percent)
    
    # Normalized dominant frequency
    descriptors['norm_fdom'] = calculate_norm_fdom_cpu(freqs, magnitude)
    
    return descriptors

def calculate_slope_cpu(freqs, signal):
    """Calculate spectral slope using least squares fit."""
    if len(freqs) < 2:
        return np.float32(0.0)
    
    # Use log scale to avoid numerical issues
    log_freqs = np.log(freqs + 1e-10)
    log_signal = np.log(signal + 1e-10)
    
    # Least squares fit
    n = len(log_freqs)
    sum_x = np.sum(log_freqs)
    sum_y = np.sum(log_signal)
    sum_xy = np.sum(log_freqs * log_signal)
    sum_x2 = np.sum(log_freqs * log_freqs)
    
    denominator = n * sum_x2 - sum_x * sum_x
    if abs(denominator) < 1e-10:
        return np.float32(0.0)
    
    slope = (n * sum_xy - sum_x * sum_y) / denominator
    return np.float32(slope)

def calculate_decrease_cpu(signal):
    """Calculate spectral decrease."""
    if len(signal) <= 1:
        return np.float32(0.0)
    
    k = np.arange(1, len(signal))
    numerator = np.sum((signal[1:] - signal[0]) / k)
    denominator = np.sum(signal)
    
    if denominator < 1e-10:
        return np.float32(0.0)
    
    return np.float32(numerator / denominator)

def calculate_hfc_cpu(freqs, magnitude):
    """Calculate High Frequency Content."""
    if len(freqs) == 0:
        return np.float32(0.0)
    
    # Weight by frequency
    hfc = np.sum(freqs * magnitude)
    return np.float32(hfc)

def calculate_bandwidth_cpu(freqs, magnitude, p):
    """Calculate spectral bandwidth."""
    if len(freqs) == 0:
        return np.float32(0.0)
    
    # Centroid
    total_power = np.sum(magnitude)
    if total_power < 1e-10:
        return np.float32(0.0)
    
    centroid = np.sum(freqs * magnitude) / total_power
    
    # Bandwidth
    bandwidth = np.sum(((freqs - centroid) ** p) * magnitude) / total_power
    return np.float32(bandwidth ** (1/p))

def calculate_rolloff_cpu(freqs, magnitude, roll_percent):
    """Calculate spectral rolloff."""
    if len(freqs) == 0:
        return np.float32(0.0)
    
    cumsum = np.cumsum(magnitude)
    total = cumsum[-1]
    
    if total < 1e-10:
        return np.float32(0.0)
    
    threshold = total * roll_percent
    rolloff_idx = np.where(cumsum >= threshold)[0]
    
    if len(rolloff_idx) == 0:
        return freqs[-1]
    
    return np.float32(freqs[rolloff_idx[0]])

def calculate_norm_fdom_cpu(freqs, magnitude):
    """Calculate normalized dominant frequency."""
    if len(freqs) == 0:
        return np.float32(0.0)
    
    # Find peaks
    peaks, _ = find_peaks(magnitude, height=np.max(magnitude) * 0.1)
    
    if len(peaks) == 0:
        # Use maximum as dominant frequency
        max_idx = np.argmax(magnitude)
        fdom = freqs[max_idx]
    else:
        # Use highest peak
        peak_heights = magnitude[peaks]
        max_peak_idx = peaks[np.argmax(peak_heights)]
        fdom = freqs[max_peak_idx]
    
    # Normalize by Nyquist frequency
    nyquist = freqs[-1] if len(freqs) > 0 else 1.0
    return np.float32(fdom / nyquist)
```

### Step 3.4: `utils/gpu_utils.py`

This file handles GPU initialization and provides a unified interface for GPU functions.

**Action:** Create a new file `utils/gpu_utils.py` and add the following code:

```python
# utils/gpu_utils.py
import logging

def initialize_gpu():
    """
    Initialize GPU functionality and return available GPU functions.
    
    Returns:
        tuple: (gpu_available: bool, gpu_functions: dict)
    """
    try:
        import cupy as cp
        import torch
        
        # Check if CUDA is available
        if not torch.cuda.is_available():
            logging.info("CUDA not available through PyTorch")
            return False, {}
        
        # Check if CuPy can access GPU
        try:
            cp.cuda.Device(0).use()
            logging.info("GPU initialized successfully")
            
            # Import GPU functions
            from dlogst_spec_descriptor_gpu import (
                dlogst_spec_descriptor_gpu,
                dlogst_spec_descriptor_gpu_2d_chunked
            )
            
            gpu_functions = {
                'single_trace': dlogst_spec_descriptor_gpu,
                'batch_traces': dlogst_spec_descriptor_gpu_2d_chunked
            }
            
            return True, gpu_functions
            
        except Exception as e:
            logging.warning(f"GPU initialization failed: {e}")
            return False, {}
            
    except ImportError as e:
        logging.info(f"GPU libraries not available: {e}")
        return False, {}
    except Exception as e:
        logging.error(f"Unexpected error during GPU initialization: {e}")
        return False, {}

def get_gpu_memory_info():
    """Get GPU memory information if available."""
    try:
        import torch
        if torch.cuda.is_available():
            device = torch.cuda.current_device()
            total_memory = torch.cuda.get_device_properties(device).total_memory
            allocated_memory = torch.cuda.memory_allocated(device)
            cached_memory = torch.cuda.memory_reserved(device)
            
            return {
                'total_memory_gb': total_memory / (1024**3),
                'allocated_memory_gb': allocated_memory / (1024**3),
                'cached_memory_gb': cached_memory / (1024**3),
                'free_memory_gb': (total_memory - allocated_memory) / (1024**3)
            }
    except Exception as e:
        logging.warning(f"Could not get GPU memory info: {e}")
    
    return None
```

### Step 3.5: `utils/general_utils.py`

This file contains miscellaneous helper functions that don't fit in other, more specific utility modules.

**Action:** Create a new file `utils/general_utils.py` and add the following code:

```python
# utils/general_utils.py
import numpy as np
import math
from tqdm import tqdm

def parse_polyline_string(polyline_str):
    """
    Parses a string of coordinates into a list of (x, y) tuples.
    Handles various delimiters like spaces, commas, and newlines.
    """
    points = []
    # Replace commas and semicolons with spaces, then split by any whitespace
    parts = polyline_str.replace(',', ' ').replace(';', ' ').split()
    if len(parts) % 2 != 0:
        raise ValueError("Polyline string must contain an even number of coordinates (pairs of X and Y).")
    for i in range(0, len(parts), 2):
        try:
            x = float(parts[i])
            y = float(parts[i+1])
            points.append((x, y))
        except ValueError:
            raise ValueError(f"Could not parse coordinate pair: '{parts[i]}', '{parts[i+1]}'")
    return points

def distance_point_to_segment(px, py, x1, y1, x2, y2):
    """
    Calculate the minimum distance from a point (px, py) to a line segment ((x1, y1), (x2, y2)).
    """
    line_mag_sq = (x2 - x1)**2 + (y2 - y1)**2
    if line_mag_sq < 1e-9: # Segment is a point
        return math.sqrt((px - x1)**2 + (py - y1)**2)
    
    u = ((px - x1) * (x2 - x1) + (py - y1) * (y2 - y1)) / line_mag_sq
    
    if u < 0.0 or u > 1.0:
        # Closest point is one of the endpoints
        dist1 = math.sqrt((px - x1)**2 + (py - y1)**2)
        dist2 = math.sqrt((px - x2)**2 + (py - y2)**2)
        return min(dist1, dist2)
    else:
        # Closest point is on the segment
        ix = x1 + u * (x2 - x1)
        iy = y1 + u * (y2 - y1)
        return math.sqrt((px - ix)**2 + (py - iy)**2)

def find_traces_near_polyline(header_loader, polyline_points, tolerance):
    """
    Finds all trace indices within a given tolerance of a polyline.
    """
    trace_coords = np.column_stack((header_loader.x_coords, header_loader.y_coords))
    nearby_indices = []

    for i in tqdm(range(len(trace_coords)), desc="Finding traces near polyline"):
        px, py = trace_coords[i]
        min_dist = float('inf')
        
        for j in range(len(polyline_points) - 1):
            x1, y1 = polyline_points[j]
            x2, y2 = polyline_points[j+1]
            dist = distance_point_to_segment(px, py, x1, y1, x2, y2)
            if dist < min_dist:
                min_dist = dist
        
        if min_dist <= tolerance:
            nearby_indices.append(header_loader.unique_indices[i])
            
    return nearby_indices
```

### Step 3.6: `utils/visualization.py`

This file will contain all plotting functions, keeping the main application logic clean.

**Action:** Create a new file `utils/visualization.py` and add the following code:

```python
# utils/visualization.py
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import numpy as np
import plotly.express as px
from common.constants import REVERSE_ATTR_NAME_MAP

def plot_basemap_with_wells(header_loader, well_data=None):
    """Plots the seismic basemap and optional well locations."""
    fig = go.Figure()

    # Add seismic trace locations
    fig.add_trace(go.Scatter(
        x=header_loader.x_coords,
        y=header_loader.y_coords,
        mode='markers',
        marker=dict(size=2, color='blue', opacity=0.6),
        name='Seismic Traces',
        hovertemplate='X: %{x}<br>Y: %{y}<extra></extra>'
    ))

    # Add well locations if available
    if well_data is not None and not well_data.empty:
        fig.add_trace(go.Scatter(
            x=well_data['X'],
            y=well_data['Y'],
            mode='markers+text',
            marker=dict(size=8, color='red', symbol='triangle-up'),
            text=well_data['Well'],
            textposition="top center",
            name='Wells',
            hovertemplate='Well: %{text}<br>X: %{x}<br>Y: %{y}<extra></extra>'
        ))

    fig.update_layout(
        title="Seismic Basemap",
        xaxis_title="X Coordinate",
        yaxis_title="Y Coordinate",
        legend_title="Legend",
        height=600,
        showlegend=True
    )
    fig.update_yaxes(scaleanchor="x", scaleratio=1)
    return fig

def plot_trace_with_descriptors(trace_data, descriptors, dt, plot_settings, selected_outputs):
    """
    Creates a multi-panel plot for a single trace and its calculated descriptors.
    """
    num_plots = len(selected_outputs)
    if num_plots == 0:
        return go.Figure()

    # Create subplot layout
    cols = min(3, num_plots)  # Max 3 columns
    rows = (num_plots + cols - 1) // cols  # Ceiling division
    
    fig = make_subplots(
        rows=rows, cols=cols,
        subplot_titles=selected_outputs,
        shared_yaxes=True,
        vertical_spacing=0.1,
        horizontal_spacing=0.1
    )
    
    time_vector = np.arange(len(trace_data)) * dt * 1000  # Convert to milliseconds

    for i, output_name in enumerate(selected_outputs):
        row = (i // cols) + 1
        col = (i % cols) + 1
        
        if output_name == "Input Signal":
            fig.add_trace(
                go.Scatter(
                    x=trace_data, y=time_vector,
                    name=output_name,
                    mode='lines',
                    line=dict(width=1)
                ),
                row=row, col=col
            )
            fig.update_xaxes(title_text="Amplitude", row=row, col=col)
            
        elif output_name == "Magnitude Spectrogram":
            if 'magnitude' in descriptors:
                freq_vector = np.fft.fftfreq(len(trace_data), dt)[:len(descriptors['magnitude'])]
                # Create 2D spectrogram (time vs frequency)
                spectrogram = np.abs(np.fft.fft(trace_data.reshape(-1, 1), axis=0))[:len(freq_vector)]
                fig.add_trace(
                    go.Heatmap(
                        z=spectrogram,
                        y=freq_vector,
                        x=time_vector,
                        colorscale='Viridis',
                        name=output_name
                    ),
                    row=row, col=col
                )
                fig.update_xaxes(title_text="Time (ms)", row=row, col=col)
                fig.update_yaxes(title_text="Frequency (Hz)", row=row, col=col)
            
        elif output_name == "Magnitude * Voice":
            if 'mag_voice' in descriptors:
                fig.add_trace(
                    go.Scatter(
                        x=descriptors['mag_voice'], y=time_vector,
                        name=output_name,
                        mode='lines',
                        line=dict(width=1, color='orange')
                    ),
                    row=row, col=col
                )
                fig.update_xaxes(title_text="Magnitude*Voice", row=row, col=col)
        
        else:
            # Handle other descriptors
            internal_name = get_internal_name(output_name)
            if internal_name in descriptors:
                data_to_plot = descriptors[internal_name]
                if isinstance(data_to_plot, np.ndarray):
                    if data_to_plot.ndim == 1:
                        # 1D descriptor
                        if len(data_to_plot) == len(time_vector):
                            fig.add_trace(
                                go.Scatter(
                                    x=data_to_plot, y=time_vector,
                                    name=output_name,
                                    mode='lines',
                                    line=dict(width=1)
                                ),
                                row=row, col=col
                            )
                        else:
                            # Frequency domain data
                            freq_vector = np.fft.fftfreq(len(trace_data), dt)[:len(data_to_plot)]
                            fig.add_trace(
                                go.Scatter(
                                    x=freq_vector, y=data_to_plot,
                                    name=output_name,
                                    mode='lines',
                                    line=dict(width=1)
                                ),
                                row=row, col=col
                            )
                            fig.update_xaxes(title_text="Frequency (Hz)", row=row, col=col)
                    else:
                        # 2D descriptor (spectrogram)
                        fig.add_trace(
                            go.Heatmap(
                                z=data_to_plot,
                                y=time_vector,
                                colorscale='RdBu',
                                zmid=0,
                                name=output_name
                            ),
                            row=row, col=col
                        )
                        fig.update_xaxes(title_text="Frequency Bin", row=row, col=col)
                
                fig.update_xaxes(title_text=output_name, row=row, col=col)

    # Update layout
    fig.update_layout(
        title_text="Single Trace Analysis",
        height=300 * rows,
        showlegend=False
    )
    
    # Update y-axes to show time in reverse (depth-like display)
    for i in range(1, rows + 1):
        for j in range(1, cols + 1):
            fig.update_yaxes(
                title_text="Time (ms)" if j == 1 else "",
                autorange="reversed",
                row=i, col=j
            )

    return fig

def plot_descriptor_section(trace_data_list, descriptors_list, dt, plot_settings, output_name):
    """
    Creates a 2D section plot for a single descriptor across multiple traces.
    """
    if not descriptors_list:
        return go.Figure()
        
    internal_name = get_internal_name(output_name)
    
    if output_name == "Input Signal":
        section_data = trace_data_list
    else:
        section_data = []
        for desc_dict in descriptors_list:
            if internal_name in desc_dict:
                data = desc_dict[internal_name]
                if isinstance(data, np.ndarray) and data.ndim == 1:
                    section_data.append(data)
                elif isinstance(data, (int, float)):
                    # Scalar descriptor - create array
                    section_data.append(np.full(len(trace_data_list[0]), data))

    if not section_data:
        return go.Figure()

    # Pad traces to the same length
    max_len = max(len(t) for t in section_data)
    padded_section = np.array([
        np.pad(t, (0, max_len - len(t)), 'constant', constant_values=0)
        for t in section_data
    ])

    time_vector = np.arange(max_len) * dt * 1000  # Convert to milliseconds
    trace_numbers = np.arange(len(section_data))

    # Apply plot limits if available
    vmin = plot_settings.get(f"{internal_name}_p5", None)
    vmax = plot_settings.get(f"{internal_name}_p95", None)
    
    if vmin is None or vmax is None:
        vmin = np.percentile(padded_section, 5)
        vmax = np.percentile(padded_section, 95)

    fig = go.Figure(data=go.Heatmap(
        z=padded_section.T,
        x=trace_numbers,
        y=time_vector,
        colorscale='RdBu',
        zmid=0,
        zmin=vmin,
        zmax=vmax,
        colorbar=dict(title=output_name)
    ))

    fig.update_layout(
        title=f"Section Plot: {output_name}",
        xaxis_title="Trace Number",
        yaxis_title="Time (ms)",
        height=600
    )
    fig.update_yaxes(autorange="reversed")
    return fig

def plot_interactive_basemap(header_loader, well_data=None):
    """
    Creates an interactive basemap for polyline selection.
    """
    fig = plot_basemap_with_wells(header_loader, well_data)
    
    # Add instructions
    fig.add_annotation(
        text="Click points to define polyline",
        xref="paper", yref="paper",
        x=0.02, y=0.98,
        showarrow=False,
        bgcolor="white",
        bordercolor="black",
        borderwidth=1
    )
    
    return fig

def get_internal_name(display_name):
    """Convert display name to internal attribute name."""
    from common.constants import ATTR_NAME_MAP
    return ATTR_NAME_MAP.get(display_name, display_name.lower().replace(' ', '_'))
```

### Step 3.6: `utils/export_utils.py`

A small helper module for functions related to the data export process.

**Action:** Overwrite the existing `utils/export_utils.py` with the following code:

```python
# utils/export_utils.py
import numpy as np

def select_export_attributes(descriptor_dict, desired_attributes):
    """
    Filters a list of desired export attributes against what is available
    in a calculated descriptor dictionary.
    
    Args:
        descriptor_dict (dict): A dictionary containing calculated descriptor arrays.
        desired_attributes (list): A list of internal attribute names to export.
        
    Returns:
        dict: A dictionary with keys as attribute names and values as the data arrays,
              containing only the attributes that were both desired and available.
    """
    available_for_export = {}
    for attr in desired_attributes:
        if attr in descriptor_dict and isinstance(descriptor_dict[attr], np.ndarray):
            available_for_export[attr] = descriptor_dict[attr]
    return available_for_export
```

---

## Phase 4: Create Main App and Pages

This is the final phase, where we build the Streamlit user interface by creating the main `app.py` and the individual page files.

### Step 4.1: `app.py` (Main Entry Point)

This file acts as the main router. It does not render any pages itself but sets up the application and provides a consistent entry point.

**Action:** Create a new file named `app.py` in the root directory and add the following code:

```python
# app.py
import streamlit as st
from common import constants, session_state
from utils import gpu_utils
import logging
import os

# --- Basic Configuration ---
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

st.set_page_config(
    page_title=constants.APP_TITLE,
    page_icon="🌊",
    layout="wide",
    initial_sidebar_state="expanded"
)

# --- CSS Styling ---
st.markdown("""
<style>
    .main-header {
        font-size: 2.5rem;
        font-weight: bold;
        color: #1f4e79;
        margin-bottom: 1rem;
    }
    .step-header {
        font-size: 1.5rem;
        color: #2c5282;
        margin-bottom: 0.5rem;
    }
    .info-box {
        background-color: #e6f3ff;
        border-left: 4px solid #1f4e79;
        padding: 1rem;
        margin: 1rem 0;
    }
    .warning-box {
        background-color: #fff3cd;
        border-left: 4px solid #ffc107;
        padding: 1rem;
        margin: 1rem 0;
    }
</style>
""", unsafe_allow_html=True)

# --- Title ---
st.markdown('<div class="main-header">🌊 WOSS Seismic Analysis Tool</div>', unsafe_allow_html=True)

# --- Initialization ---
if 'session_initialized' not in st.session_state:
    session_state.initialize_session_state()
    
    # Check for GPU and store result in session state
    gpu_available, gpu_funcs = gpu_utils.initialize_gpu()
    st.session_state.gpu_available = gpu_available
    st.session_state.gpu_functions = gpu_funcs
    
    if gpu_available:
        st.sidebar.success("🚀 GPU acceleration enabled")
    else:
        st.sidebar.info("💻 Using CPU processing")

    st.session_state.session_initialized = True

# --- Sidebar Navigation ---
st.sidebar.markdown("### 📋 Workflow Navigation")

# Progress tracking
progress_items = [
    ("📥 Load Data", st.session_state.get('header_loader') is not None),
    ("⚙️ Configure", st.session_state.get('plot_settings') is not None),
    ("🎯 Select Area", st.session_state.get('area_selected', False)),
    ("🔬 Analyze", st.session_state.get('analysis_complete', False)),
    ("📊 Export", st.session_state.get('export_complete', False))
]

for item_name, is_complete in progress_items:
    status_icon = "✅" if is_complete else "⏳"
    st.sidebar.markdown(f"{status_icon} {item_name}")

st.sidebar.markdown("---")

# System Information
with st.sidebar.expander("💻 System Information"):
    st.write(f"**GPU Available:** {'Yes' if st.session_state.gpu_available else 'No'}")
    if st.session_state.gpu_available:
        gpu_info = gpu_utils.get_gpu_memory_info()
        if gpu_info:
            st.write(f"**GPU Memory:** {gpu_info['total_memory_gb']:.1f} GB")
            st.write(f"**Available:** {gpu_info['free_memory_gb']:.1f} GB")

# Reset button
if st.sidebar.button("🔄 Start New Analysis", use_container_width=True, type="secondary"):
    session_state.reset_state()
    st.rerun()

# --- Main Content ---
st.markdown("## Welcome to the WOSS Seismic Analysis Tool")

col1, col2 = st.columns([2, 1])

with col1:
    st.markdown("""
    This application provides advanced spectral analysis capabilities for seismic data:
    
    - **Multi-format Support**: Load SEG-Y files and well data
    - **GPU Acceleration**: Utilize CUDA for faster processing
    - **Interactive Visualization**: Explore data with modern plotting tools
    - **Flexible Analysis**: Single trace, inline, crossline, or full volume analysis
    - **Export Capabilities**: Save results in multiple formats
    """)

with col2:
    st.image("https://via.placeholder.com/300x200?text=Seismic+Analysis", caption="WOSS Analysis Workflow")

# --- Current Status ---
st.markdown("---")
st.markdown("## 📊 Current Status")

if st.session_state.get('header_loader'):
    st.success("✅ Data loaded successfully!")
    info = st.session_state.header_loader.get_inline_crossline_range()
    
    col1, col2, col3, col4 = st.columns(4)
    col1.metric("📏 Inline Range", f"{info['inline_min']}-{info['inline_max']}")
    col2.metric("📐 Crossline Range", f"{info['xline_min']}-{info['xline_max']}")
    col3.metric("🔢 Total Traces", f"{st.session_state.trace_count:,}")
    col4.metric("⏱️ Sample Rate", f"{st.session_state.dt*1000:.1f} ms")
    
    if st.session_state.get('area_selected'):
        st.info(f"🎯 Selected {len(st.session_state.selected_indices)} traces for analysis")
    
    if st.session_state.get('analysis_complete'):
        st.success("🔬 Analysis completed successfully!")
        
else:
    st.markdown('<div class="info-box">ℹ️ <strong>Getting Started:</strong> Please navigate to <strong>1_Load_Data</strong> to begin by uploading your SEG-Y file.</div>', unsafe_allow_html=True)

# --- Quick Start Guide ---
with st.expander("🚀 Quick Start Guide"):
    st.markdown("""
    ### Step-by-Step Process:
    
    1. **📥 Load Data**: Upload your SEG-Y file and optional well data
    2. **⚙️ Configure**: Set analysis parameters and calculate statistics
    3. **🎯 Select Area**: Choose traces for analysis (single trace, lines, or volume)
    4. **🔬 Analyze**: Run the spectral analysis calculations
    5. **📊 Export**: Save results and generate reports
    
    ### Tips:
    - Use the sidebar navigation to move between steps
    - GPU acceleration will automatically be used if available
    - Statistics calculation helps set optimal display ranges
    - Large datasets are processed in batches for memory efficiency
    """)

# --- Footer ---
st.markdown("---")
st.markdown("*WOSS Tool v2.0 - Advanced Seismic Spectral Analysis*")
```

### Step 4.2: `pages/1_load_data.py`

This page handles the uploading of SEG-Y and well data files.

**Action:** Create a new file `pages/1_load_data.py` and add the following code:

```python
# pages/1_load_data.py
import streamlit as st
from utils.data_utils import SegyHeaderLoader, load_excel_data, get_sampling_interval, get_trace_count
import tempfile
import os
import logging

st.set_page_config(page_title="Load Data", layout="wide")
st.title("Step 1: Load Data")

# --- Functions ---
@st.cache_data
def load_segy_headers_cached(segy_file_path, inline_byte, xline_byte, x_byte, y_byte, scaler_byte, use_custom_scaler):
    """Cached function to load SEG-Y headers."""
    header_loader = SegyHeaderLoader(segy_file_path)
    header_loader.load_headers(inline_byte, xline_byte, x_byte, y_byte, scaler_byte, use_custom_scaler)
    return header_loader

@st.cache_data
def load_excel_data_cached(uploaded_file):
    """Cached function to load well data from Excel."""
    return load_excel_data(uploaded_file)

# --- UI ---
st.sidebar.header("Data Input")
segy_file = st.sidebar.file_uploader("Upload SEG-Y File", type=['sgy', 'segy'])
well_file = st.sidebar.file_uploader("Upload Well Data (Optional Excel)", type=['xlsx'])

st.sidebar.header("SEG-Y Header Configuration")
c1, c2 = st.sidebar.columns(2)
inline_byte = c1.number_input("Inline Byte", value=189)
xline_byte = c2.number_input("Crossline Byte", value=193)
x_byte = c1.number_input("X-Coord Byte", value=73)
y_byte = c2.number_input("Y-Coord Byte", value=77)

scaler_mode = st.sidebar.radio("Coordinate Scaler", ["Use Scaler Byte", "Use Custom Scaler"], index=0)
if scaler_mode == "Use Scaler Byte":
    scaler_byte = st.sidebar.number_input("Scaler Byte", value=71)
    use_custom_scaler = False
else:
    scaler_byte = st.sidebar.number_input("Custom Scaler Value", value=1.0)
    use_custom_scaler = True

# --- Processing ---
if st.sidebar.button("Load Data", use_container_width=True, type="primary"):
    if segy_file is not None:
        with st.spinner("Processing SEG-Y file..."):
            # Create a temporary file to store the uploaded data
            with tempfile.NamedTemporaryFile(delete=False, suffix=".sgy") as tmp_file:
                tmp_file.write(segy_file.getvalue())
                st.session_state.segy_temp_file_path = tmp_file.name

            try:
                # Load headers using the cached function
                header_loader = load_segy_headers_cached(
                    st.session_state.segy_temp_file_path, inline_byte, xline_byte,
                    x_byte, y_byte, scaler_byte, use_custom_scaler
                )
                st.session_state.header_loader = header_loader

                # Get metadata
                st.session_state.dt = get_sampling_interval(st.session_state.segy_temp_file_path)
                st.session_state.trace_count = get_trace_count(st.session_state.segy_temp_file_path)

                st.success("SEG-Y file headers loaded successfully!")

                # Load well data if provided
                if well_file is not None:
                    st.session_state.well_data = load_excel_data_cached(well_file)
                    st.success("Well data loaded successfully!")
                
            except Exception as e:
                st.error(f"An error occurred while loading the SEG-Y file: {e}")
                logging.error(f"SEG-Y loading error: {e}", exc_info=True)

    else:
        st.sidebar.warning("Please upload a SEG-Y file.")

# --- Display Info ---
if st.session_state.get('header_loader'):
    st.subheader("Loaded Data Summary")
    info = st.session_state.header_loader.get_inline_crossline_range()
    st.write(f"**Inlines:** {info['inline_min']} to {info['inline_max']}")
    st.write(f"**Crosslines:** {info['xline_min']} to {info['xline_max']}")
    st.write(f"**Total Traces:** {st.session_state.trace_count}")
    st.write(f"**Sampling Interval:** {st.session_state.dt * 1000} ms")

    if st.session_state.get('well_data') is not None:
        st.write("**Well Data:**")
        st.dataframe(st.session_state.well_data.head())
else:
    st.info("Upload a SEG-Y file and click 'Load Data' to begin.")
```

### Step 4.3: `pages/2_configure_display.py`

This page allows the user to configure all parameters for the analysis and visualization.

**Action:** Create a new file `pages/2_configure_display.py` and add the following code:

```python
# pages/2_configure_display.py
import streamlit as st
from common import ui_elements
from utils import processing, visualization
import logging

st.set_page_config(page_title="Configure Parameters", layout="wide")
st.title("Step 2: Configure Analysis Parameters")

if 'header_loader' not in st.session_state or st.session_state.header_loader is None:
    st.warning("Please load data in Step 1 before configuring parameters.")
    st.stop()

# --- Display Basemap and SEGY Info ---
with st.expander("Basemap & SEGY Info", expanded=True):
    info = st.session_state.header_loader.get_inline_crossline_range()
    c1, c2, c3, c4 = st.columns(4)
    c1.metric("Min Inline", info['inline_min'])
    c2.metric("Max Inline", info['inline_max'])
    c3.metric("Min Crossline", info['xline_min'])
    c4.metric("Max Crossline", info['xline_max'])

    if st.button("Show Basemap"):
        with st.spinner("Generating basemap..."):
            fig = visualization.plot_basemap_with_wells(
                st.session_state.header_loader,
                st.session_state.get('well_data')
            )
            st.plotly_chart(fig, use_container_width=True)

# --- Parameter Configuration ---
st.header("Parameter Configuration")

# Tabs for different settings
tab_general, tab_spectral, tab_stats, tab_display = st.tabs(["General", "Spectral", "Statistics", "Display Limits"])

with tab_general:
    st.subheader("General Settings")
    st.session_state.plot_settings['epsilon'] = st.number_input(
        "Epsilon (for numerical stability)", value=1e-10, format="%e"
    )
    st.session_state.plot_settings['fdom_exponent'] = st.number_input(
        "Dominant Frequency Exponent (for WOSS)", value=2.0, step=0.1
    )

with tab_spectral:
    st.subheader("Spectral Descriptor Settings")
    st.session_state.plot_settings['use_band_limited'] = st.checkbox("Use Band-Limited for Spectral Slope/Decrease", value=False)
    c1, c2 = st.columns(2)
    st.session_state.plot_settings['b1'] = c1.number_input("b1 (Hz)", value=5.0)
    st.session_state.plot_settings['b2'] = c2.number_input("b2 (Hz)", value=40.0)

    st.subheader("Logistic Transform Settings")
    c1, c2, c3 = st.columns(3)
    st.session_state.plot_settings['kmax'] = c1.number_input("kmax", value=120.0)
    st.session_state.plot_settings['shape'] = c2.number_input("shape", value=0.35)
    st.session_state.plot_settings['int_val'] = c3.number_input("int_val", value=35.0)

    st.subheader("Bandwidth and Rolloff Settings")
    c1, c2 = st.columns(2)
    st.session_state.plot_settings['p_bandwidth'] = c1.number_input("p_bandwidth", value=2.0)
    st.session_state.plot_settings['roll_percent'] = c2.slider("roll_percent", 0.0, 1.0, 0.85)

with tab_stats:
    st.subheader("Statistics Calculation Settings")
    sample_percent = st.slider("Sample Percentage for Stats", 0.1, 100.0, 1.0, 0.1)
    max_traces_for_stats = st.number_input("Max Traces for Stats", value=50)

    if st.button("Calculate Statistics and Default Plot Limits", type="primary"):
        with st.spinner("Calculating statistics from sample traces... This may take a moment."):
            spectral_params = {
                'use_band_limited': st.session_state.plot_settings.get('use_band_limited', False),
                'shape': st.session_state.plot_settings.get('shape', 1.0),
                'kmax': st.session_state.plot_settings.get('kmax', 1.0),
                'int_val': st.session_state.plot_settings.get('int_val', 1.0),
                'b1': st.session_state.plot_settings.get('b1', 0.01),
                'b2': st.session_state.plot_settings.get('b2', 0.1),
                'p_bandwidth': st.session_state.plot_settings.get('p_bandwidth', 0.5),
                'roll_percent': st.session_state.plot_settings.get('roll_percent', 0.85),
            }
            
            # Determine which spectral descriptor function to use
            spec_descriptor_func = st.session_state.gpu_functions.get('single_trace')
            if not spec_descriptor_func:
                from dlogst_spec_descriptor_cpu import dlogst_spec_descriptor_cpu
                spec_descriptor_func = dlogst_spec_descriptor_cpu

            results = processing.calculate_stats_and_defaults(
                st.session_state.segy_temp_file_path,
                st.session_state.dt,
                sample_percent,
                max_traces_for_stats,
                spectral_params,
                spec_descriptor_func
            )
            if results:
                st.session_state.stats_results = results.get('stats')
                st.session_state.normalization_defaults = results.get('defaults')
                st.success("Statistics calculated successfully!")
                # Apply defaults to plot settings
                st.session_state.plot_settings.update(st.session_state.normalization_defaults)
            else:
                st.error("Failed to calculate statistics.")

with tab_display:
    st.subheader("Plot Display Limits")
    if st.session_state.get('normalization_defaults'):
        st.info("Default limits have been populated from statistics. You can adjust them below.")
    
    defaults = st.session_state.get('normalization_defaults', {})
    
    c1, c2 = st.columns(2)
    st.session_state.plot_settings['data_p5'] = c1.number_input("Input Signal Min (P5)", value=float(defaults.get('data_p5', -1.0)))
    st.session_state.plot_settings['data_p95'] = c2.number_input("Input Signal Max (P95)", value=float(defaults.get('data_p95', 1.0)))
    
    st.session_state.plot_settings['woss_p5'] = c1.number_input("WOSS Min (P5)", value=float(defaults.get('woss_p5', -1.0)))
    st.session_state.plot_settings['woss_p95'] = c2.number_input("WOSS Max (P95)", value=float(defaults.get('woss_p95', 1.0)))
    
    # You can add more limit controls for other descriptors as needed

if st.session_state.get('stats_results'):
    with st.expander("View Calculated Statistics"):
        st.json(st.session_state.stats_results)

st.markdown("---")
if st.button("Confirm Settings and Proceed", use_container_width=True):
    st.success("Parameters have been configured.")
    st.balloons()

```

### Step 4.4: `pages/3_select_area.py`

This page handles the UI for selecting the specific area or traces for analysis.

**Action:** Create a new file `pages/3_select_area.py` and add the following code:

```python
# pages/3_select_area.py
import streamlit as st
from utils import general_utils, data_utils
import logging
import pandas as pd

st.set_page_config(page_title="Select Area", layout="wide")
st.title("Step 3: Select Area for Analysis")

if 'header_loader' not in st.session_state or st.session_state.header_loader is None:
    st.warning("Please load data in Step 1 before selecting an area.")
    st.stop()

# --- Mode Selection ---
st.session_state.analysis_mode = st.sidebar.selectbox(
    "Select Analysis Mode",
    ("Single Trace from Well", "Single Inline", "Single Crossline", "Traces near Polyline", "Full Volume AOI")
)

# --- UI for selected mode ---
mode = st.session_state.analysis_mode

if mode == "Single Trace from Well":
    st.header("Select Well Marker")
    if st.session_state.get('well_data') is not None:
        well_data = st.session_state.well_data
        well_marker_list = [f"{row['Well']} - {row['Surface']}" for _, row in well_data.iterrows()]
        selected_pair_str = st.selectbox("Select a Well-Marker pair:", well_marker_list)
        
        if st.button("Find Nearest Trace", type="primary"):
            selected_row = well_data[well_data.apply(lambda r: f"{r['Well']} - {r['Surface']}" == selected_pair_str, axis=1)]
            if not selected_row.empty:
                well_x, well_y = selected_row.iloc[0]['X'], selected_row.iloc[0]['Y']
                with st.spinner("Finding nearest trace..."):
                    trace_index = data_utils.get_nearest_trace_index(st.session_state.header_loader, well_x, well_y)
                    st.session_state.selected_indices = [trace_index]
                    st.session_state.area_selected = True
                    st.success(f"Found nearest trace at index: {trace_index}")
            else:
                st.error("Could not find the selected well-marker pair.")
    else:
        st.warning("No well data loaded. Please upload well data in Step 1.")

elif mode == "Single Inline":
    st.header("Select Inline")
    info = st.session_state.header_loader.get_inline_crossline_range()
    inline_choice = st.slider("Select Inline Number", info['inline_min'], info['inline_max'], info['inline_min'])
    
    if st.button("Select All Traces on Inline", type="primary"):
        all_inlines = st.session_state.header_loader.inlines
        # Find the indices of the traces that match the chosen inline
        chosen_indices = st.session_state.header_loader.unique_indices[all_inlines == inline_choice]
        st.session_state.selected_indices = chosen_indices
        st.session_state.area_selected = True
        st.success(f"Selected {len(chosen_indices)} traces for inline {inline_choice}.")

elif mode == "Single Crossline":
    st.header("Select Crossline")
    info = st.session_state.header_loader.get_inline_crossline_range()
    crossline_choice = st.slider("Select Crossline Number", info['xline_min'], info['xline_max'], info['xline_min'])

    if st.button("Select All Traces on Crossline", type="primary"):
        all_crosslines = st.session_state.header_loader.crosslines
        chosen_indices = st.session_state.header_loader.unique_indices[all_crosslines == crossline_choice]
        st.session_state.selected_indices = chosen_indices
        st.session_state.area_selected = True
        st.success(f"Selected {len(chosen_indices)} traces for crossline {crossline_choice}.")

elif mode == "Traces near Polyline":
    st.header("Define Polyline")
    polyline_str = st.text_area("Enter Polyline Coordinates (X Y pairs, separated by spaces or newlines):", "x1 y1\nx2 y2\nx3 y3")
    tolerance = st.number_input("Tolerance around polyline", value=50.0)

    if st.button("Find Traces near Polyline", type="primary"):
        try:
            polyline_points = general_utils.parse_polyline_string(polyline_str)
            with st.spinner("Finding traces near polyline..."):
                indices = general_utils.find_traces_near_polyline(st.session_state.header_loader, polyline_points, tolerance)
                st.session_state.selected_indices = indices
                st.session_state.area_selected = True
                st.success(f"Found {len(indices)} traces near the polyline.")
        except ValueError as e:
            st.error(f"Error parsing polyline: {e}")

elif mode == "Full Volume AOI":
    st.header("Define Area of Interest (AOI)")
    info = st.session_state.header_loader.get_inline_crossline_range()
    
    c1, c2 = st.columns(2)
    min_il, max_il = c1.slider("Inline Range", info['inline_min'], info['inline_max'], (info['inline_min'], info['inline_max']))
    min_xl, max_xl = c2.slider("Crossline Range", info['xline_min'], info['xline_max'], (info['xline_min'], info['xline_max']))

    if st.button("Select Traces in AOI", type="primary"):
        inlines = st.session_state.header_loader.inlines
        crosslines = st.session_state.header_loader.crosslines
        
        # Create boolean masks
        inline_mask = (inlines >= min_il) & (inlines <= max_il)
        crossline_mask = (crosslines >= min_xl) & (crosslines <= max_xl)
        
        # Combine masks
        combined_mask = inline_mask & crossline_mask
        
        chosen_indices = st.session_state.header_loader.unique_indices[combined_mask]
        st.session_state.selected_indices = chosen_indices
        st.session_state.area_selected = True
        st.success(f"Selected {len(chosen_indices)} traces within the AOI.")

if st.session_state.get('area_selected'):
    st.info(f"**{len(st.session_state.selected_indices)}** traces have been selected for analysis.")
    st.markdown("You can now proceed to **Step 4: Analyze Data**.")
```

### Step 4.5: `pages/4_analyze_data.py`

This page is the core of the application, where the actual data processing and analysis happen.

**Action:** Create a new file `pages/4_analyze_data.py` and add the following code:

```python
# pages/4_analyze_data.py
import streamlit as st
from utils import data_utils, processing, visualization
import numpy as np
import logging
from tqdm import tqdm

st.set_page_config(page_title="Analyze Data", layout="wide")
st.title("Step 4: Analyze Data")

if not st.session_state.get('area_selected'):
    st.warning("Please select an area in Step 3 before running the analysis.")
    st.stop()

# --- Analysis ---
if st.button("Run Analysis", type="primary", use_container_width=True):
    indices_to_process = st.session_state.selected_indices
    segy_path = st.session_state.segy_temp_file_path
    dt = st.session_state.dt
    plot_settings = st.session_state.plot_settings
    
    # Determine which spectral descriptor function to use
    spec_descriptor_func = st.session_state.gpu_functions.get('single_trace')
    if not spec_descriptor_func:
        from dlogst_spec_descriptor_cpu import dlogst_spec_descriptor_cpu
        spec_descriptor_func = dlogst_spec_descriptor_cpu
        st.info("Using CPU for processing.")
    else:
        st.info("Using GPU for processing.")

    all_descriptors = []
    all_trace_data = []
    
    progress_bar = st.progress(0, text="Starting analysis...")

    try:
        for i, trace_idx in enumerate(indices_to_process):
            trace_sample = data_utils.load_trace_sample(segy_path, trace_idx)
            if trace_sample is None:
                logging.warning(f"Skipping invalid trace at index {trace_idx}")
                continue

            # Prepare spectral parameters, removing WOSS-specific ones
            spectral_params = {k: v for k, v in plot_settings.items() if k not in ['epsilon', 'fdom_exponent', 'hfc_p95']}
            
            fmax_calc = (1 / (dt * 2)) * 0.8 # 80% of Nyquist
            descriptor = spec_descriptor_func(trace_sample, dt, fmax=fmax_calc, **spectral_params)
            
            # Calculate WOSS separately
            descriptor['WOSS'] = processing.calculate_woss(descriptor, plot_settings)
            descriptor['data'] = trace_sample # Add original data for plotting/export

            all_trace_data.append(trace_sample)
            all_descriptors.append(descriptor)
            
            progress_bar.progress((i + 1) / len(indices_to_process), text=f"Processing trace {i+1}/{len(indices_to_process)}")

        st.session_state.loaded_trace_data = all_trace_data
        st.session_state.calculated_descriptors = all_descriptors
        st.session_state.analysis_complete = True
        progress_bar.empty()
        st.success(f"Analysis complete for {len(all_descriptors)} traces.")

    except Exception as e:
        st.error(f"An error occurred during analysis: {e}")
        logging.error(f"Analysis error: {e}", exc_info=True)


# --- Display Results ---
if st.session_state.get('analysis_complete'):
    st.header("Analysis Results")

    analysis_mode = st.session_state.analysis_mode
    
    if analysis_mode == "Single Trace from Well":
        st.subheader("Single Trace Analysis")
        from common.constants import AVAILABLE_OUTPUTS_SINGLE
        selected_outputs = st.multiselect("Select outputs to display:", AVAILABLE_OUTPUTS_SINGLE, default=["Input Signal", "WOSS"])
        
        if st.session_state.loaded_trace_data:
            fig = visualization.plot_trace_with_descriptors(
                st.session_state.loaded_trace_data[0],
                st.session_state.calculated_descriptors[0],
                st.session_state.dt,
                st.session_state.plot_settings,
                selected_outputs
            )
            st.plotly_chart(fig, use_container_width=True)

    else: # For Inline, Crossline, Polyline, AOI
        st.subheader("Section Analysis")
        from common.constants import AVAILABLE_OUTPUTS_SECTION
        output_to_plot = st.selectbox("Select descriptor to display as a section:", AVAILABLE_OUTPUTS_SECTION, index=len(AVAILABLE_OUTPUTS_SECTION)-1)
        
        with st.spinner(f"Generating section plot for {output_to_plot}..."):
            fig = visualization.plot_descriptor_section(
                st.session_state.loaded_trace_data,
                st.session_state.calculated_descriptors,
                st.session_state.dt,
                st.session_state.plot_settings,
                output_to_plot
            )
            st.plotly_chart(fig, use_container_width=True)

```

### Step 4.6: `pages/5_export_results.py`

This final page handles the exporting of the calculated results into new SEG-Y files.

**Action:** Create a new file `pages/5_export_results.py` and add the following code:

```python
# pages/5_export_results.py
import streamlit as st
from utils import export_utils, data_utils
from common.constants import EXPORTABLE_ATTR_DISPLAY_NAMES, ATTR_NAME_MAP
import os
import tempfile
import zipfile
import shutil
import segyio
import numpy as np
import logging
from tqdm import tqdm
import time

st.set_page_config(page_title="Export Results", layout="wide")
st.title("Step 5: Export Results")

if not st.session_state.get('analysis_complete'):
    st.warning("Please run an analysis in Step 4 before exporting.")
    st.stop()

# --- Export Configuration ---
st.header("📊 Export Configuration")

# Attribute Selection
selected_display_names = st.multiselect(
    "Select attributes to export:",
    EXPORTABLE_ATTR_DISPLAY_NAMES,
    default=[
        "Original Seismic Amplitude",
        "WOSS (Weighted-Optimum Spectral Shape)",
        "High Frequency Content (HFC)",
        "Spectral Bandwidth"
    ]
)

if not selected_display_names:
    st.warning("Please select at least one attribute to export.")
    st.stop()

attributes_to_export = [ATTR_NAME_MAP[name] for name in selected_display_names]

# Export Options
col1, col2 = st.columns(2)

with col1:
    st.subheader("📁 Export Format")
    export_format = st.selectbox(
        "Choose export format:",
        ["SEG-Y Files", "NumPy Arrays", "Both"]
    )
    
    include_metadata = st.checkbox("Include metadata file", value=True)
    compress_output = st.checkbox("Compress output (ZIP)", value=True)

with col2:
    st.subheader("⚙️ Processing Options")
    header_loader = st.session_state.header_loader
    
    # Batching configuration
    grouping_type = st.selectbox("Group batches by:", ["Inline", "Crossline", "Trace Index"])
    
    # Suggest batch size based on number of traces
    suggested_batch_size = get_suggested_batch_size_for_export(len(st.session_state.selected_indices))
    batch_size = st.number_input(
        "Number of groups per batch file:",
        min_value=1,
        max_value=1000,
        value=suggested_batch_size
    )

# Preview Export Summary
with st.expander("📋 Export Summary"):
    st.write(f"**Number of traces:** {len(st.session_state.loaded_trace_data)}")
    st.write(f"**Attributes to export:** {len(selected_display_names)}")
    st.write(f"**Export format:** {export_format}")
    st.write(f"**Grouping:** {grouping_type}")
    st.write(f"**Batch size:** {batch_size}")
    
    # Estimate file sizes
    estimated_size = estimate_export_size(
        len(st.session_state.loaded_trace_data),
        len(st.session_state.loaded_trace_data[0]) if st.session_state.loaded_trace_data else 1000,
        len(selected_display_names),
        export_format
    )
    st.write(f"**Estimated total size:** {estimated_size}")

# --- Export Process ---
if st.button("📦 Generate Export Files", type="primary", use_container_width=True):
    try:
        # Create temporary directory for export
        export_dir = tempfile.mkdtemp(prefix="woss_export_")
        st.session_state.export_dir = export_dir
        
        # Progress tracking
        progress_bar = st.progress(0)
        status_text = st.empty()
        
        status_text.text("Preparing export data...")
        
        # Organize data by grouping type
        grouped_data = organize_data_for_export(
            st.session_state.loaded_trace_data,
            st.session_state.calculated_descriptors,
            st.session_state.selected_indices,
            header_loader,
            grouping_type
        )
        
        progress_bar.progress(0.1)
        status_text.text("Creating export files...")
        
        # Export files
        export_files = []
        total_groups = len(grouped_data)
        
        for i, (group_key, group_data) in enumerate(grouped_data.items()):
            # Create batch files
            batch_files = create_batch_files(
                group_data, attributes_to_export, export_dir, 
                group_key, export_format, batch_size
            )
            export_files.extend(batch_files)
            
            # Update progress
            progress = 0.1 + (i + 1) / total_groups * 0.7
            progress_bar.progress(progress)
            status_text.text(f"Processed group {i+1}/{total_groups}")
        
        # Create metadata file
        if include_metadata:
            metadata_file = create_metadata_file(export_dir, selected_display_names)
            export_files.append(metadata_file)
        
        progress_bar.progress(0.9)
        status_text.text("Finalizing export...")
        
        # Create ZIP file if requested
        if compress_output:
            zip_path = os.path.join(export_dir, "woss_export_results.zip")
            create_zip_file(export_files, zip_path)
            final_file = zip_path
        else:
            final_file = export_dir
        
        # Store export information
        st.session_state.export_complete = True
        st.session_state.last_export_path = final_file
        st.session_state.export_files = export_files
        
        progress_bar.progress(1.0)
        status_text.text("Export completed successfully!")
        
        st.success(f"✅ Export completed! {len(export_files)} files created.")
        
    except Exception as e:
        st.error(f"❌ Export failed: {e}")
        logging.error(f"Export error: {e}", exc_info=True)

# --- Download Results ---
if st.session_state.get('export_complete'):
    st.header("📥 Download Results")
    
    # Summary of exported files
    col1, col2, col3 = st.columns(3)
    col1.metric("Files Created", len(st.session_state.export_files))
    col2.metric("Total Size", get_directory_size(st.session_state.export_dir))
    col3.metric("Format", export_format)
    
    # Download options
    if compress_output and os.path.exists(st.session_state.last_export_path):
        with open(st.session_state.last_export_path, 'rb') as f:
            st.download_button(
                label="📥 Download ZIP File",
                data=f.read(),
                file_name="woss_export_results.zip",
                mime="application/zip",
                use_container_width=True
            )
    
    # Individual file downloads
    with st.expander("📄 Individual Files"):
        for file_path in st.session_state.export_files:
            if os.path.exists(file_path):
                file_name = os.path.basename(file_path)
                file_size = os.path.getsize(file_path)
                
                col1, col2 = st.columns([3, 1])
                col1.write(f"**{file_name}** ({file_size / 1024:.1f} KB)")
                
                with col2:
                    with open(file_path, 'rb') as f:
                        st.download_button(
                            label="📥",
                            data=f.read(),
                            file_name=file_name,
                            key=f"download_{file_name}"
                        )

# --- Helper Functions ---
def get_suggested_batch_size_for_export(num_traces):
    """Suggest appropriate batch size based on number of traces."""
    if num_traces <= 100:
        return 10
    elif num_traces <= 1000:
        return 50
    else:
        return 100

def estimate_export_size(num_traces, samples_per_trace, num_attributes, export_format):
    """Estimate the total size of exported files."""
    # Rough estimate: 4 bytes per sample for float32
    bytes_per_trace = samples_per_trace * 4 * num_attributes
    total_bytes = num_traces * bytes_per_trace
    
    if export_format == "Both":
        total_bytes *= 2  # Double for both formats
    
    # Add overhead for SEG-Y headers
    if export_format in ["SEG-Y Files", "Both"]:
        total_bytes += num_traces * 240  # 240 bytes per trace header
    
    # Convert to human-readable format
    if total_bytes < 1024**2:
        return f"{total_bytes / 1024:.1f} KB"
    elif total_bytes < 1024**3:
        return f"{total_bytes / (1024**2):.1f} MB"
    else:
        return f"{total_bytes / (1024**3):.1f} GB"

def organize_data_for_export(trace_data, descriptors, indices, header_loader, grouping_type):
    """Organize data by grouping type for export."""
    grouped_data = {}
    
    for i, (trace, desc, idx) in enumerate(zip(trace_data, descriptors, indices)):
        if grouping_type == "Inline":
            group_key = f"inline_{header_loader.inlines[idx]}"
        elif grouping_type == "Crossline":
            group_key = f"crossline_{header_loader.crosslines[idx]}"
        else:  # Trace Index
            group_key = f"traces_{i // 100}"  # Group by hundreds
        
        if group_key not in grouped_data:
            grouped_data[group_key] = []
        
        grouped_data[group_key].append({
            'trace_data': trace,
            'descriptors': desc,
            'index': idx
        })
    
    return grouped_data

def create_batch_files(group_data, attributes_to_export, export_dir, group_key, export_format, batch_size):
    """Create batch files for a group of data."""
    batch_files = []
    
    for i in range(0, len(group_data), batch_size):
        batch_data = group_data[i:i + batch_size]
        batch_name = f"{group_key}_batch_{i // batch_size + 1}"
        
        if export_format in ["SEG-Y Files", "Both"]:
            segy_file = os.path.join(export_dir, f"{batch_name}.sgy")
            create_segy_file(batch_data, attributes_to_export, segy_file)
            batch_files.append(segy_file)
        
        if export_format in ["NumPy Arrays", "Both"]:
            numpy_file = os.path.join(export_dir, f"{batch_name}.npz")
            create_numpy_file(batch_data, attributes_to_export, numpy_file)
            batch_files.append(numpy_file)
    
    return batch_files

def create_segy_file(batch_data, attributes_to_export, output_path):
    """Create a SEG-Y file from batch data."""
    # This is a simplified implementation
    # In practice, you'd need to properly handle SEG-Y format specifications
    logging.info(f"Creating SEG-Y file: {output_path}")
    
    # For now, create a placeholder file
    with open(output_path, 'w') as f:
        f.write("# SEG-Y export placeholder\n")
        f.write(f"# Attributes: {', '.join(attributes_to_export)}\n")
        f.write(f"# Number of traces: {len(batch_data)}\n")

def create_numpy_file(batch_data, attributes_to_export, output_path):
    """Create a NumPy file from batch data."""
    export_data = {}
    
    for attr in attributes_to_export:
        attr_data = []
        for item in batch_data:
            if attr in item['descriptors']:
                attr_data.append(item['descriptors'][attr])
            elif attr == 'data':
                attr_data.append(item['trace_data'])
        
        if attr_data:
            export_data[attr] = np.array(attr_data)
    
    np.savez_compressed(output_path, **export_data)

def create_metadata_file(export_dir, selected_display_names):
    """Create a metadata file with export information."""
    metadata_file = os.path.join(export_dir, "export_metadata.txt")
    
    with open(metadata_file, 'w') as f:
        f.write("WOSS Export Metadata\n")
        f.write("=" * 50 + "\n")
        f.write(f"Export Date: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"Exported Attributes: {', '.join(selected_display_names)}\n")
        f.write(f"Number of Traces: {len(st.session_state.loaded_trace_data)}\n")
        f.write(f"Analysis Mode: {st.session_state.analysis_mode}\n")
        f.write(f"Sampling Interval: {st.session_state.dt * 1000:.1f} ms\n")
        
        # Add parameter settings
        f.write("\nAnalysis Parameters:\n")
        f.write("-" * 20 + "\n")
        for key, value in st.session_state.plot_settings.items():
            f.write(f"{key}: {value}\n")
    
    return metadata_file

def create_zip_file(file_list, zip_path):
    """Create a ZIP file containing all export files."""
    with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
        for file_path in file_list:
            if os.path.exists(file_path):
                arcname = os.path.basename(file_path)
                zipf.write(file_path, arcname)

def get_directory_size(directory):
    """Get the total size of a directory."""
    total_size = 0
    for dirpath, dirnames, filenames in os.walk(directory):
        for filename in filenames:
            file_path = os.path.join(dirpath, filename)
            total_size += os.path.getsize(file_path)
    
    # Convert to human-readable format
    if total_size < 1024**2:
        return f"{total_size / 1024:.1f} KB"
    elif total_size < 1024**3:
        return f"{total_size / (1024**2):.1f} MB"
    else:
        return f"{total_size / (1024**3):.1f} GB"
```

---

## Phase 5: Additional Considerations and Completion

### Step 5.1: Error Handling and Logging

**Action:** Ensure all modules have proper error handling and logging:

1. **Add try-catch blocks** around all file operations
2. **Use logging.error()** for critical errors
3. **Use logging.warning()** for non-critical issues
4. **Use logging.info()** for important status updates

### Step 5.2: Testing and Validation

**Action:** Before deploying, test the refactored application:

1. **Unit Testing**: Test individual functions in isolation
2. **Integration Testing**: Test the complete workflow
3. **Performance Testing**: Compare processing times with the original app
4. **Memory Testing**: Ensure no memory leaks during batch processing

### Step 5.3: Documentation

**Action:** Create comprehensive documentation:

1. **User Guide**: Step-by-step instructions for using the application
2. **API Documentation**: Document all functions and their parameters
3. **Configuration Guide**: Explain all available parameters and their effects
4. **Troubleshooting Guide**: Common issues and solutions

### Step 5.4: Deployment Considerations

**Action:** Prepare for deployment:

1. **Environment Setup**: Document Python version and dependency requirements
2. **Configuration Files**: Create sample configuration files
3. **Docker Support**: Consider creating a Dockerfile for containerized deployment
4. **Performance Optimization**: Profile and optimize critical code paths

---

## Summary

This refactoring guide transforms the monolithic WOSS application into a clean, maintainable, and scalable multi-page Streamlit application. The key improvements include:

1. **Modular Architecture**: Separation of concerns with dedicated modules
2. **Improved User Experience**: Modern, intuitive interface with progress tracking
3. **Enhanced Performance**: Efficient batch processing and GPU acceleration
4. **Better Error Handling**: Comprehensive error handling and logging
5. **Flexible Export Options**: Multiple export formats and compression options
6. **Comprehensive Documentation**: Detailed implementation guide and best practices

The refactored application maintains all the original functionality while providing a foundation for future enhancements and easier maintenance.

## Critical Missing Implementations

Based on the analysis of the current codebase, several key components are missing or incomplete. Here are the critical items that need to be implemented:

### 1. **CRITICAL: Missing CPU Implementation**
⚠️ **THE MOST CRITICAL MISSING COMPONENT** ⚠️

The current codebase **DOES NOT HAVE ANY CPU IMPLEMENTATION** for spectral descriptor calculations. It only has:
- GPU implementation in `dlogst_spec_descriptor_gpu.py`
- Fallback stubs in `app_ref.py` that raise `NotImplementedError`

**Current problematic code in `app_ref.py`:**
```python
def dlogst_spec_descriptor_gpu(*args, **kwargs):
    st.error("GPU function dlogst_spec_descriptor_gpu not available.")
    raise NotImplementedError("GPU function dlogst_spec_descriptor_gpu not available.")
```

**This means the application will completely fail on systems without GPU support.**

You **MUST** implement the CPU version first before any refactoring:
- Create `utils/dlogst_spec_descriptor_cpu.py` with the complete CPU implementation
- This is the most critical missing piece that will break the application
- The CPU implementation provided in Step 3.3 is complete and ready to use

### 2. **Directory Structure Creation**
The current codebase still uses the flat structure. You need to create the directory structure first:

```bash
mkdir common pages utils
```

### 3. **Import Path Issues**
The current `app_ref.py` has import statements that won't work with the new structure:
```python
from data_utils import SegyHeaderLoader  # This needs to be: from utils.data_utils import SegyHeaderLoader
```

### 4. **Session State Management**
The `common/session_state.py` module needs to be implemented before any of the pages can work properly.

### 5. **Constants Definition**
The `common/constants.py` file contains all the constant definitions that are referenced throughout the application.

### 6. **Streamlit Configuration**
The `.streamlit/config.toml` file should be created for better user experience:

```toml
[theme]
primaryColor = "#1f4e79"
backgroundColor = "#ffffff"
secondaryBackgroundColor = "#f0f2f6"
textColor = "#262730"

[server]
maxUploadSize = 1000
```

### 7. **MANDATORY Implementation Order**

⚠️ **CRITICAL: You MUST follow this exact order to avoid breaking the application:**

1. **FIRST: Create CPU Implementation** 
   - Create `utils/dlogst_spec_descriptor_cpu.py` (use the complete code from Step 3.3)
   - This is THE MOST CRITICAL step - without it, the app won't work on CPU-only systems

2. **Create directory structure** (common, pages, utils)
3. **Create requirements.txt** with all dependencies
4. **Implement common modules** (constants.py, session_state.py, ui_elements.py)
5. **Move and update existing utils** (data_utils.py, processing.py, gpu_utils.py, etc.)
6. **Create the main app.py** file
7. **Implement pages** in order (1_load_data.py, 2_configure_display.py, etc.)
8. **Test each page** individually before moving to the next
9. **Fix import issues** and dependency problems
10. **Add error handling** and logging throughout
11. **Test the complete workflow**

### 8. **Configuration Management**

Create a `config.py` file in the root directory to manage application settings:

```python
# config.py
import os
from pathlib import Path

# Application settings
APP_NAME = "WOSS Seismic Analysis Tool"
APP_VERSION = "2.0.0"
DEBUG = os.getenv("DEBUG", "False").lower() == "true"

# File paths
PROJECT_ROOT = Path(__file__).parent
DATA_DIR = PROJECT_ROOT / "data"
TEMP_DIR = PROJECT_ROOT / "temp"
EXPORT_DIR = PROJECT_ROOT / "exports"

# Processing settings
DEFAULT_BATCH_SIZE = 100
MAX_TRACES_FOR_STATS = 1000
DEFAULT_SAMPLE_PERCENT = 1.0

# GPU settings
ENABLE_GPU = os.getenv("ENABLE_GPU", "True").lower() == "true"
GPU_MEMORY_FRACTION = float(os.getenv("GPU_MEMORY_FRACTION", "0.8"))

# Create directories if they don't exist
for directory in [DATA_DIR, TEMP_DIR, EXPORT_DIR]:
    directory.mkdir(exist_ok=True)
```

### 9. **Environment Setup Script**

Create a `setup.py` or `environment.yml` file for easier environment setup:

```yaml
# environment.yml
name: woss-analysis
channels:
  - conda-forge
  - defaults
dependencies:
  - python=3.9
  - streamlit>=1.28.0
  - numpy>=1.21.0
  - pandas>=1.3.0
  - plotly>=5.0.0
  - scipy>=1.7.0
  - tqdm>=4.62.0
  - pip
  - pip:
    - segyio>=1.9.0
    - torch>=1.9.0
    # Add cupy for GPU support (optional)
    # - cupy-cuda11x>=9.0.0
```

### 10. **Migration Script**

Create a migration script to help transition from the old structure to the new one:

```python
# migrate.py
import os
import shutil
from pathlib import Path

def migrate_old_structure():
    """Migrate from old flat structure to new modular structure."""
    
    # Create new directories
    directories = ['common', 'pages', 'utils']
    for directory in directories:
        Path(directory).mkdir(exist_ok=True)
    
    # Move files to appropriate locations
    file_mappings = {
        'data_utils.py': 'utils/data_utils.py',
        'processing.py': 'utils/processing.py',
        'visualization.py': 'utils/visualization.py',
        'export_utils.py': 'utils/export_utils.py',
        'utils.py': 'utils/general_utils.py',
        'dlogst_spec_descriptor_gpu.py': 'utils/dlogst_spec_descriptor_gpu.py',
    }
    
    for old_path, new_path in file_mappings.items():
        if os.path.exists(old_path):
            shutil.move(old_path, new_path)
            print(f"Moved {old_path} to {new_path}")
    
    print("Migration completed!")

if __name__ == "__main__":
    migrate_old_structure()
```

### 11. **Testing Framework**

Create a basic testing framework:

```python
# tests/test_basic.py
import pytest
import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from utils.processing import calculate_woss, clean_array
import numpy as np

def test_calculate_woss():
    """Test WOSS calculation function."""
    descriptor = {
        'hfc': np.array([1.0, 2.0, 3.0]),
        'norm_fdom': np.array([0.1, 0.2, 0.3]),
        'mag_voice_slope': np.array([0.5, 1.0, 1.5])
    }
    
    plot_settings = {
        'epsilon': 1e-4,
        'fdom_exponent': 2.0,
        'hfc_p95': 1.0
    }
    
    result = calculate_woss(descriptor, plot_settings)
    assert isinstance(result, np.ndarray)
    assert len(result) == 3

def test_clean_array():
    """Test array cleaning function."""
    arr = np.array([1.0, np.inf, 2.0, np.nan, 3.0])
    cleaned = clean_array(arr)
    assert len(cleaned) == 3
    assert np.array_equal(cleaned, np.array([1.0, 2.0, 3.0]))

if __name__ == "__main__":
    pytest.main([__file__])
```

---

## Implementation Status Checklist

Use this checklist to track your refactoring progress:

### Phase 1: Setup
- [ ] Create directory structure (common, pages, utils)
- [ ] Create requirements.txt
- [ ] Create .streamlit/config.toml
- [ ] Create config.py for application settings

### Phase 2: Common Modules
- [ ] Implement common/constants.py
- [ ] Implement common/session_state.py
- [ ] Implement common/ui_elements.py

### Phase 3: Utilities
- [ ] Implement utils/data_utils.py
- [ ] Implement utils/processing.py
- [ ] Implement utils/dlogst_spec_descriptor_cpu.py
- [ ] Implement utils/gpu_utils.py
- [ ] Implement utils/visualization.py
- [ ] Implement utils/general_utils.py
- [ ] Implement utils/export_utils.py

### Phase 4: Main Application
- [ ] Implement app.py
- [ ] Fix all import statements
- [ ] Test basic application startup

### Phase 5: Pages
- [ ] Implement pages/1_load_data.py
- [ ] Implement pages/2_configure_display.py
- [ ] Implement pages/3_select_area.py
- [ ] Implement pages/4_analyze_data.py
- [ ] Implement pages/5_export_results.py

### Phase 6: Testing & Validation
- [ ] Test each page individually
- [ ] Test complete workflow
- [ ] Fix any remaining import issues
- [ ] Add comprehensive error handling
- [ ] Performance testing

### Phase 7: Documentation
- [ ] Create user documentation
- [ ] Create API documentation
- [ ] Create deployment guide
- [ ] Create troubleshooting guide

This checklist ensures nothing is missed during the refactoring process.