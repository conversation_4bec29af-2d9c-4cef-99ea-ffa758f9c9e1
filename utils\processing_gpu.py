# utils/processing_gpu.py
import numpy as np
import logging
from tqdm import tqdm
from .gpu_utils import get_processing_functions, get_optimal_processing_config, clear_gpu_memory
from .data_utils import load_trace_sample

def process_inline_analysis_gpu(segy_path, inline_number, header_loader, dt, plot_settings):
    """GPU-optimized processing for single inline analysis."""
    logging.info(f"Starting GPU-optimized inline {inline_number} analysis")

    # Filter traces for the selected inline
    inline_mask = header_loader.inlines == inline_number
    trace_indices = header_loader.unique_indices[inline_mask]
    crosslines = header_loader.crosslines[inline_mask]

    # Get optimal processing configuration
    config = get_optimal_processing_config("Single inline (all crosslines)", len(trace_indices))

    # Process with GPU prioritization
    trace_data, descriptors = process_traces_gpu_batch(
        segy_path, trace_indices, dt, plot_settings, config
    )

    return {
        'trace_data': trace_data,
        'descriptors': descriptors,
        'crosslines': crosslines,
        'processing_config': config
    }

def process_crossline_mode_gpu(segy_path, crossline_number, header_loader, dt, plot_settings):
    """GPU-optimized processing for single crossline mode."""
    backend_type, descriptor_func = get_processing_backend()
    batch_size = optimize_batch_size_for_mode("Single crossline (all inlines)")
    
    logging.info(f"Processing crossline {crossline_number} using {backend_type} backend")
    
    # Get all traces for this crossline
    crossline_mask = header_loader.crosslines == crossline_number
    trace_indices = header_loader.unique_indices[crossline_mask]
    
    return process_traces_gpu_batch(segy_path, trace_indices, dt, plot_settings, 
                                   descriptor_func, batch_size)

def process_aoi_mode_gpu(segy_path, inline_range, crossline_range, header_loader, dt, plot_settings):
    """GPU-optimized processing for AOI (Area of Interest) mode."""
    backend_type, descriptor_func = get_processing_backend()
    batch_size = optimize_batch_size_for_mode("By inline/crossline section (AOI)")
    
    logging.info(f"Processing AOI using {backend_type} backend")
    
    # Get traces within AOI bounds
    inline_mask = (header_loader.inlines >= inline_range[0]) & (header_loader.inlines <= inline_range[1])
    crossline_mask = (header_loader.crosslines >= crossline_range[0]) & (header_loader.crosslines <= crossline_range[1])
    aoi_mask = inline_mask & crossline_mask
    trace_indices = header_loader.unique_indices[aoi_mask]
    
    return process_traces_gpu_batch(segy_path, trace_indices, dt, plot_settings, 
                                   descriptor_func, batch_size)

def process_polyline_mode_gpu(segy_path, polyline_indices, header_loader, dt, plot_settings):
    """GPU-optimized processing for polyline mode."""
    backend_type, descriptor_func = get_processing_backend()
    batch_size = optimize_batch_size_for_mode("By Polyline File Import")
    
    logging.info(f"Processing polyline with {len(polyline_indices)} traces using {backend_type} backend")
    
    return process_traces_gpu_batch(segy_path, polyline_indices, dt, plot_settings, 
                                   descriptor_func, batch_size)

def process_traces_gpu_batch(segy_path, trace_indices, dt, plot_settings, descriptor_func, batch_size):
    """Core GPU batch processing function for all modes."""
    backend_type, _ = get_processing_backend()
    
    if backend_type == "GPU":
        return _process_gpu_optimized(segy_path, trace_indices, dt, plot_settings, descriptor_func, batch_size)
    else:
        return _process_cpu_fallback(segy_path, trace_indices, dt, plot_settings, descriptor_func, batch_size)

def _process_gpu_optimized(segy_path, trace_indices, dt, plot_settings, descriptor_func, batch_size):
    """GPU-optimized batch processing implementation."""
    import cupy as cp
    from tqdm import tqdm
    
    all_trace_data = []
    all_descriptors = []
    
    # Process in optimized GPU batches
    for i in tqdm(range(0, len(trace_indices), batch_size), desc="GPU Processing"):
        batch_indices = trace_indices[i:i + batch_size]
        
        # Load batch to GPU memory
        batch_traces = []
        for idx in batch_indices:
            trace = load_trace_sample(segy_path, idx)
            batch_traces.append(trace)
        
        # Convert to GPU arrays
        gpu_traces = cp.array(batch_traces, dtype=cp.float32)
        
        # Process entire batch on GPU
        batch_descriptors = descriptor_func(gpu_traces, dt, **plot_settings)
        
        # Convert results back to CPU for storage
        cpu_descriptors = {}
        for key, value in batch_descriptors.items():
            if hasattr(value, 'get'):  # CuPy array
                cpu_descriptors[key] = cp.asnumpy(value)
            else:
                cpu_descriptors[key] = value
        
        all_trace_data.extend(batch_traces)
        all_descriptors.append(cpu_descriptors)
        
        # Clear GPU memory
        del gpu_traces, batch_descriptors
        cp.get_default_memory_pool().free_all_blocks()
    
    return all_trace_data, all_descriptors

def _process_cpu_fallback(segy_path, trace_indices, dt, plot_settings, descriptor_func, batch_size):
    """CPU fallback processing implementation."""
    from tqdm import tqdm
    
    all_trace_data = []
    all_descriptors = []
    
    # Smaller batches for CPU processing
    cpu_batch_size = min(batch_size // 4, 32)
    
    for i in tqdm(range(0, len(trace_indices), cpu_batch_size), desc="CPU Processing"):
        batch_indices = trace_indices[i:i + cpu_batch_size]
        
        for idx in batch_indices:
            trace = load_trace_sample(segy_path, idx)
            descriptors = descriptor_func(trace, dt, **plot_settings)
            
            all_trace_data.append(trace)
            all_descriptors.append(descriptors)
    
    return all_trace_data, all_descriptors

# Import load_trace_sample function
def load_trace_sample(segy_path, trace_index):
    """Load a single trace sample from SEG-Y file."""
    # This will be imported from data_utils in the actual implementation
    # For now, provide a placeholder that will be replaced
    try:
        from .data_utils import load_trace_sample as load_trace
        return load_trace(segy_path, trace_index)
    except ImportError:
        # Fallback implementation
        import segyio
        try:
            with segyio.open(segy_path, 'r', ignore_geometry=True) as segyfile:
                if trace_index < 0 or trace_index >= segyfile.tracecount:
                    logging.warning(f"Trace index {trace_index} is out of bounds.")
                    return None
                return segyfile.trace.raw[trace_index]
        except Exception as e:
            logging.error(f"Failed to load trace {trace_index} from {segy_path}: {e}")
            return None
