# common/session_state.py
"""
GPU-aware session state management for WOSS Seismic Analysis Tool.

This module handles session state initialization with GPU processing preferences
and maintains state consistency across the modular Streamlit application.
"""

import streamlit as st
import tempfile
import logging
from .constants import GPU_PROCESSING_PREFERRED, DEFAULT_PROCESSING_MODE, DEFAULT_GPU_MEMORY_MODE

def initialize_session_state():
    """Initialize session state with GPU processing preferences."""
    
    # GPU Processing State
    if 'gpu_available' not in st.session_state:
        st.session_state.gpu_available = False
    if 'gpu_backend' not in st.session_state:
        st.session_state.gpu_backend = "Unknown"
    if 'gpu_device_info' not in st.session_state:
        st.session_state.gpu_device_info = None
    if 'processing_mode' not in st.session_state:
        st.session_state.processing_mode = DEFAULT_PROCESSING_MODE
    if 'gpu_memory_mode' not in st.session_state:
        st.session_state.gpu_memory_mode = DEFAULT_GPU_MEMORY_MODE
        
    # Analysis Mode State
    if 'selected_analysis_mode' not in st.session_state:
        st.session_state.selected_analysis_mode = None
    if 'gpu_batch_size' not in st.session_state:
        st.session_state.gpu_batch_size = 512
    if 'area_selected' not in st.session_state:
        st.session_state.area_selected = False
        
    # Data Loading State
    if 'header_loader' not in st.session_state:
        st.session_state.header_loader = None
    if 'segy_file_path' not in st.session_state:
        st.session_state.segy_file_path = None
    if 'segy_file_info' not in st.session_state:
        st.session_state.segy_file_info = None
    if 'well_df' not in st.session_state:
        st.session_state.well_df = None
    if 'dt' not in st.session_state:
        st.session_state.dt = None
    if 'trace_count' not in st.session_state:
        st.session_state.trace_count = None
        
    # Processing Configuration State
    if 'plot_settings' not in st.session_state:
        st.session_state.plot_settings = None
    if 'stats_defaults' not in st.session_state:
        st.session_state.stats_defaults = None
    if 'selected_outputs' not in st.session_state:
        st.session_state.selected_outputs = []
        
    # Processing Results State  
    if 'gpu_processing_results' not in st.session_state:
        st.session_state.gpu_processing_results = None
    if 'processing_backend_used' not in st.session_state:
        st.session_state.processing_backend_used = None
    if 'analysis_complete' not in st.session_state:
        st.session_state.analysis_complete = False
    if 'loaded_trace_data' not in st.session_state:
        st.session_state.loaded_trace_data = None
    if 'calculated_descriptors' not in st.session_state:
        st.session_state.calculated_descriptors = None
        
    # Area Selection State
    if 'selected_inline' not in st.session_state:
        st.session_state.selected_inline = None
    if 'selected_crossline' not in st.session_state:
        st.session_state.selected_crossline = None
    if 'aoi_bounds' not in st.session_state:
        st.session_state.aoi_bounds = None
    if 'polyline_indices' not in st.session_state:
        st.session_state.polyline_indices = None
    if 'selected_markers' not in st.session_state:
        st.session_state.selected_markers = None
        
    # Export State
    if 'export_config' not in st.session_state:
        st.session_state.export_config = {}
    if 'export_results' not in st.session_state:
        st.session_state.export_results = None
    if 'export_attributes' not in st.session_state:
        st.session_state.export_attributes = []
    if 'export_in_progress' not in st.session_state:
        st.session_state.export_in_progress = False
    if 'exported_files_info' not in st.session_state:
        st.session_state.exported_files_info = None
        
    # Navigation State
    if 'current_step' not in st.session_state:
        st.session_state.current_step = "load_data"
        
    # Temporary File Management
    if 'segy_temp_file_path' not in st.session_state:
        st.session_state.segy_temp_file_path = None
    if 'export_output_dir' not in st.session_state:
        st.session_state.export_output_dir = None

def reset_state():
    """Reset session state for new analysis while preserving GPU info."""
    # Preserve GPU information
    gpu_info = {
        'gpu_available': st.session_state.get('gpu_available', False),
        'gpu_backend': st.session_state.get('gpu_backend', "Unknown"),
        'gpu_device_info': st.session_state.get('gpu_device_info', None),
        'processing_mode': st.session_state.get('processing_mode', DEFAULT_PROCESSING_MODE),
        'gpu_memory_mode': st.session_state.get('gpu_memory_mode', DEFAULT_GPU_MEMORY_MODE)
    }
    
    # Clear all session state except GPU info
    keys_to_preserve = set(gpu_info.keys())
    for key in list(st.session_state.keys()):
        if key not in keys_to_preserve:
            del st.session_state[key]
    
    # Restore GPU info and reinitialize
    st.session_state.update(gpu_info)
    initialize_session_state()
    
    logging.info("Session state reset - GPU preferences preserved")

def update_gpu_state(gpu_available, gpu_backend, gpu_device_info=None):
    """Update GPU state information."""
    st.session_state.gpu_available = gpu_available
    st.session_state.gpu_backend = gpu_backend
    st.session_state.gpu_device_info = gpu_device_info
    
    logging.info(f"GPU state updated: Available={gpu_available}, Backend={gpu_backend}")

def get_gpu_status():
    """Get current GPU status information."""
    return {
        'available': st.session_state.get('gpu_available', False),
        'backend': st.session_state.get('gpu_backend', "Unknown"),
        'device_info': st.session_state.get('gpu_device_info', None),
        'processing_mode': st.session_state.get('processing_mode', DEFAULT_PROCESSING_MODE),
        'memory_mode': st.session_state.get('gpu_memory_mode', DEFAULT_GPU_MEMORY_MODE)
    }

def is_analysis_ready():
    """Check if all required data is loaded for analysis."""
    return (
        st.session_state.get('header_loader') is not None and
        st.session_state.get('segy_file_path') is not None and
        st.session_state.get('dt') is not None and
        st.session_state.get('plot_settings') is not None and
        st.session_state.get('area_selected', False)
    )

def get_analysis_progress():
    """Get current analysis progress information."""
    steps_completed = 0
    total_steps = 5  # load_data, configure, select_area, analyze, export
    
    if st.session_state.get('header_loader') is not None:
        steps_completed += 1
    if st.session_state.get('plot_settings') is not None:
        steps_completed += 1
    if st.session_state.get('area_selected', False):
        steps_completed += 1
    if st.session_state.get('analysis_complete', False):
        steps_completed += 1
    if st.session_state.get('export_results') is not None:
        steps_completed += 1
        
    return {
        'completed_steps': steps_completed,
        'total_steps': total_steps,
        'percentage': (steps_completed / total_steps) * 100,
        'current_step': st.session_state.get('current_step', 'load_data')
    }
